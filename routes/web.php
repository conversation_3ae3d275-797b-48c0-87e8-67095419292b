<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\SpecificHelpController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\SubscriptionController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return redirect()->route('login');
});

Route::group([
    'middleware' => 'auth'
], function () {
    Route::group(['prefix' => 'home', 'as' => 'home.'], function () {
        Route::get('/', [HomeController::class, 'index'])->name('home');
        Route::get('/get-data', [HomeController::class, 'getData'])->name('getData');
    });

    Route::group(['prefix' => 'notification', 'as' => 'notification.'], function () {
        Route::get('/', [NotificationController::class, 'index'])->name('index');
        Route::get('create', [NotificationController::class, 'create'])->name('create');
        Route::post('store', [NotificationController::class, 'store'])->name('store');
        Route::get('get-userid-by-portal-type', [NotificationController::class, 'getUserIdByPortalType'])->name('getUserIdByPortalType');
        Route::get('get-cities-by-keyword', [NotificationController::class, 'getCitiesByKeyword'])->name('getCitiesByKeyword');
        Route::get('get-postalcode-by-cities', [NotificationController::class, 'getPostalCodeByCities'])->name('getPostalCodeByCities');
        Route::get('get-zipcode-by-keyword', [NotificationController::class, 'getZipcodeByKeyword'])->name('getZipcodeByKeyword');
    });

    Route::group(['prefix' => 'specific-help', 'as' => 'specific-help.'], function () {
        Route::get('/', [SpecificHelpController::class, 'index'])->name('index');
        Route::post('change-status/{id}', [SpecificHelpController::class, 'changeStatus'])->name('changeStatus');
        Route::post('reply/{id}', [SpecificHelpController::class, 'reply'])->name('reply');
    });

    Route::group(['prefix' => 'subscription', 'as' => 'subscription.'], function () {
        Route::get('/', [SubscriptionController::class, 'index'])->name('index');
    });

    Route::group(['prefix' => 'store', 'as' => 'store.'], function () {
        Route::get('import', [StoreController::class, 'showImport'])->name('showImport');
        Route::post('import', [StoreController::class, 'import'])->name('import');
        Route::get('import/process/{import}', [StoreController::class, 'processImport'])->name('processImport');
        Route::get('/', [StoreController::class, 'index'])->name('index');
        Route::get('/type/{type}', [StoreController::class, 'index'])->name('type');
        Route::get('/{id}/detail', [StoreController::class, 'detail'])->name('detail');
        Route::post('/{id}/status/{status}', [StoreController::class, 'updateStatus'])
            ->name('updateStatus');
    });

    // Store Schedule Management Routes
    Route::group(['prefix' => 'store/schedules', 'as' => 'store.schedules.'], function () {
        Route::get('/', [StoreScheduleController::class, 'index'])->name('index');
        Route::get('/{id}', [StoreScheduleController::class, 'show'])->name('show');
        Route::put('/update', [StoreScheduleController::class, 'update'])->name('update');
        Route::put('/bulk-update', [StoreScheduleController::class, 'bulkUpdate'])->name('bulkUpdate');
        Route::get('/filtered', [StoreScheduleController::class, 'getFilteredSchedules'])->name('filtered');
    });
});

Auth::routes();
