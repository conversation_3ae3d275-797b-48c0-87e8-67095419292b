<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class Store extends Model
{
    protected $table = 'stores';

    public $timestamps = false;

    protected $fillable = [
        'Status',
        'Name',
        'EmailDecrypted',
        'BusinessId',
        'APECode'
    ];

    public function subscription(): HasOne
    {
        return $this->hasOne(Subscription::class, 'StoreId', 'Id');
    }

    public function address(): HasOne
    {
        return $this->hasOne(Address::class, 'StoreId', 'Id');
    }

    public function business(): HasOne
    {
        return $this->hasOne(Business::class, 'Id', 'BusinessId');
    }

    public function category(): HasOneThrough
    {
        return $this->hasOneThrough(
            Category::class,
            StoreCategory::class,
            'StoreId',
            'Id',
            'Id',
            'CategoryId'
        );
    }

    public function categories(): HasManyThrough
    {
        return $this->hasManyThrough(
            Category::class,
            StoreCategory::class,
            'StoreId',
            'Id',
            'Id',
            'CategoryId'
        );
    }

    public function employee(): HasOneThrough
    {
        return $this->hasOneThrough(
            Employee::class,
            StoreEmployee::class,
            'StoreId',
            'Id',
            'Id',
            'EmployeeId'
        );
    }

    public function subscriptionPlan(): HasOneThrough
    {
        return $this->hasOneThrough(
            SubscriptionPlan::class,
            Subscription::class,
            'StoreId',
            'Id',
            'Id',
            'SubscriptionPlanId'
        );
    }

    public function getMainCategories()
    {
        return $this->categories->pluck('MainCategoryName')->unique()->filter()->values()->toArray();
    }

    /**
     * Get the address schedule associated with the store through address.
     */
    public function addressSchedule()
    {
        return $this->address->schedule ?? null;
    }
}
