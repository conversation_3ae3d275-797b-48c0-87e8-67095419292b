<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AddressSchedule extends Model
{
    protected $table = 'addressschedules';

    public $timestamps = false;

    protected $fillable = [
        'AddressId',
        'DayOfWeek',
        'MorningOpenTime',
        'MorningCloseTime',
        'PostalCode',
        'AfternoonOpenTime',
        'AfternoonCloseTime',
        'FromDate',
        'ToDate'
    ];

    /**
     * Get the address that owns the schedule.
     */
    public function address(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'AddressId', 'Id');
    }
}
