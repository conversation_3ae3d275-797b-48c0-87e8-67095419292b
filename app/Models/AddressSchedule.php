<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * AddressSchedule Model
 *
 * Represents the operating hours and schedule information for a store address.
 * This model handles the relationship between addresses and their operating schedules,
 * including morning and afternoon hours for different days of the week.
 *
 * @package App\Models
 */
class AddressSchedule extends Model
{
    protected $table = 'addressschedules';

    public $timestamps = false;

    protected $fillable = [
        'AddressId',
        'DayOfWeek',
        'MorningOpenTime',
        'MorningCloseTime',
        'PostalCode',
        'AfternoonOpenTime',
        'AfternoonCloseTime',
        'FromDate',
        'ToDate'
    ];

    /**
     * Get the address that owns the schedule.
     */
    public function address(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'AddressId', 'Id');
    }
}
