<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StoreEmployee extends Model
{
    protected $table = 'storeemployees';

    public $timestamps = false;

    protected $fillable = [
        'StoreId',
        'EmployeeId'
    ];

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'StoreId', 'Id');
    }

    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'EmployeeId', 'Id');
    }
}
