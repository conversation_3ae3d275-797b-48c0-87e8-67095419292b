<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Address extends Model
{
    protected $table = 'addresses';

    public $timestamps = false;

    protected $fillable = [
        'Address1',
        'Address2',
        'City',
        'PostalCode',
        'Country',
        'PhoneNumber',
        'StoreId'
    ];

    /**
     * Get the store that owns the address.
     */
    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'StoreId', 'Id');
    }

    /**
     * Get the schedule associated with the address.
     */
    public function schedule(): HasOne
    {
        return $this->hasOne(AddressSchedule::class, 'AddressId', 'Id');
    }
}
