<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SpecificHelp extends Model
{
    protected $table = 'specifichelps';

    public $timestamps = false;

    protected $fillable = [
        'Status'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(UserApp::class, 'ModifiedBy', 'Id');
    }
}
