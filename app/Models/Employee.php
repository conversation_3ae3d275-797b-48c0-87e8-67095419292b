<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class Employee extends Model
{
    protected $table = 'employees';

    public $timestamps = false;

    protected $fillable = [
        'FirstName',
        'LastName',
        'Email',
        'Phone'
    ];

    public function storeEmployees(): HasMany
    {
        return $this->hasMany(StoreEmployee::class, 'EmployeeId', 'Id');
    }

    public function stores(): HasManyThrough
    {
        return $this->hasManyThrough(
            Store::class,
            StoreEmployee::class,
            'EmployeeId',
            'Id',
            'Id',
            'StoreId'
        );
    }

    public function getFullNameAttribute(): string
    {
        return "{$this->FirstName} {$this->LastName}";
    }
}
