<?php

namespace App\Http\Controllers;

use App\Services\SpecificHelpService;
use Illuminate\Http\Request;

class SpecificHelpController extends Controller
{
    protected SpecificHelpService $specificHelpService;
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        SpecificHelpService $specificHelpService
    )
    {
        $this->specificHelpService = $specificHelpService;
    }

    public function index()
    {
        $data = $this->specificHelpService->getData();

        return view('specific-help.index', [
            'data' => $data
        ]);
    }

    public function changeStatus(string $id, Request $request)
    {
        $response = $this->specificHelpService->changeStatus($id, $request->all());

        return response()->json($response);
    }

    public function reply(string $id, Request $request)
    {
        $response = $this->specificHelpService->reply($id, $request->all());

        return redirect()->route('specific-help.index')->with(['success' => $response['message']]);
    }
}
