<?php

namespace App\Http\Controllers;

use App\Enums\StoreStatus;
use App\Http\Requests\ImportStoreRequest;
use App\Http\Requests\StoreIndexRequest;
use App\Services\StoreService;
use App\Services\Api\VikingStoreSystemService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use App\Models\Import;

/**
 * StoreController
 *
 * Controller for handling store-related HTTP requests.
 */
class StoreController extends Controller
{
    protected StoreService $storeService;
    protected VikingStoreSystemService $vikingStoreSystemService;

    /**
     * Create a new controller instance.
     *
     * @param StoreService $storeService
     * @param VikingStoreSystemService $vikingStoreSystemService
     * @return void
     */
    public function __construct(
        StoreService $storeService,
        VikingStoreSystemService $vikingStoreSystemService
    )
    {
        $this->storeService = $storeService;
        $this->vikingStoreSystemService = $vikingStoreSystemService;
    }

    /**
     * Display a listing of stores with optional filtering.
     *
     * @param StoreIndexRequest $request
     * @param string|null $type
     * @return View
     */

    public function index(StoreIndexRequest $request, string $type = null): View
    {
        $filters = $request->validated();
        $filterOptions = $this->storeService->getFilterOptions();
        $data = $this->storeService->getFilteredData($type, $filters, $request);

        return view('store.index', array_merge(
            [
                'data' => $data,
                'type' => $type
            ],
            $filterOptions
        ));
    }

    /**
     * Update the status of a store.
     *
     * @param string $id
     * @param int $status
     * @return JsonResponse
     *
     * @see StoreStatus For the status enum values
     */
    public function updateStatus(string $id, int $status): JsonResponse
    {
        $result = $this->storeService->updateStatus($id, $status);

        if ($result) {
            return response()->json(['success' => true, 'message' => 'Store status was updated successfully']);
        }
        return response()->json(['success' => false, 'message' => 'Failed to update store status'], 500);
    }

    public function showImport()
    {
        return view('store.import');
    }

    public function import(ImportStoreRequest $request)
    {
        $file = $request->file('import_file');
        $result = $this->storeService->import($file);

        return redirect()->route('store.processImport', $result['data']->id);
    }

    public function processImport(Import $import)
    {
        $data = $this->storeService->processImport($import);

        return view('store.process.detail', [
            'data' => $data
        ]);
    }

    /**
     * Display store detail with schedule management
     *
     * @param string $id
     * @return View
     */
    public function detail(string $id): View
    {
        // Get store data using the three required service methods
        $storeDetail = $this->vikingStoreSystemService->getStoreDetail($id);
        $storeServices = $this->vikingStoreSystemService->getStoreServices($id);
        $metaData = $this->vikingStoreSystemService->getStoreMasterData();

        return view('store.detail', [
            'storeDetail' => $storeDetail,
            'storeServices' => $storeServices,
            'metaData' => $metaData,
            'storeId' => $id
        ]);
    }
}
