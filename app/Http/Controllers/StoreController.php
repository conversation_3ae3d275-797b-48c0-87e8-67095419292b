<?php

namespace App\Http\Controllers;

use App\Enums\StoreStatus;
use App\Http\Requests\StoreIndexRequest;
use App\Models\Category;
use App\Services\Api\VikingStoreSystemService;
use App\Services\StoreService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use App\Models\Import;

/**
 * StoreController
 *
 * Controller for handling store-related HTTP requests.
 */
class StoreController extends Controller
{
    protected StoreService $storeService;
    protected VikingStoreSystemService $vikingStoreSystemService;

    /**
     * Create a new controller instance.
     *
     * @param StoreService $storeService
     * @return void
     */
    public function __construct(
        StoreService $storeService,
        VikingStoreSystemService $vikingStoreSystemService
    )
    {
        $this->storeService = $storeService;
        $this->vikingStoreSystemService = $vikingStoreSystemService;
    }

    /**
     * Display a listing of stores with optional filtering.
     *
     * @param StoreIndexRequest $request
     * @param string|null $type
     * @return View
     */

    public function index(StoreIndexRequest $request, string $type = null): View
    {
        $filters = $request->validated();
        $filterOptions = $this->storeService->getFilterOptions();
        $data = $this->storeService->getFilteredData($type, $filters, $request);

        return view('store.index', array_merge(
            [
                'data' => $data,
                'type' => $type
            ],
            $filterOptions
        ));
    }

    /**
     * Update the status of a store.
     *
     * @param string $id
     * @param int $status
     * @return JsonResponse
     *
     * @see StoreStatus For the status enum values
     */
    public function updateStatus(string $id, int $status): JsonResponse
    {
        $result = $this->storeService->updateStatus($id, $status);

        if ($result) {
            return response()->json(['success' => true, 'message' => 'Store status was updated successfully']);
        }
        return response()->json(['success' => false, 'message' => 'Failed to update store status'], 500);
    }

    public function showImport()
    {
        return view('store.import');
    }

    public function import(Request $request)
    {
        $file = $request->file('import_file');
        $result = $this->storeService->import($file);

        return redirect()->route('store.processImport', $result['data']->id);
    }

    public function processImport(Import $import)
    {
        $data = $this->storeService->processImport($import);

        return view('store.process.detail', [
            'data' => $data
        ]);
    }

    /**
     * Display the detailed view of a store with all tabs data.
     *
     * @param string $id
     * @return View
     */
    public function detail(string $id): View
    {
        $store = $this->storeService->getStoreWithAllRelations($id);

        if (!$store) {
            abort(404, 'Store not found');
        }

        $allCategories = Category::all();
        $storeDetail = $this->vikingStoreSystemService->getStoreDetail($id);
        $storeServices = $this->vikingStoreSystemService->getStoreServices($id);
        $metaData = $this->vikingStoreSystemService->getStoreMasterData();

        return view('store.detail', [
            'store' => $store,
            'allCategories' => $allCategories,
            'storeDetail' => $storeDetail,
            'storeServices' => $storeServices,
            'metaData' => $metaData,
        ]);
    }

}
