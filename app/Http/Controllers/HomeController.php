<?php

namespace App\Http\Controllers;


use App\Http\Requests\GetHomepageChartRequest;
use App\Services\HomeService;

class HomeController extends Controller
{
    protected HomeService $homeService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(HomeService $homeService)
    {
        $this->homeService = $homeService;
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $totalData = $this->homeService->getDataHomepage();

        return view('home',
            [
                'totalData' => $totalData
            ]
        );
    }

    public function getData(GetHomepageChartRequest $request)
    {

        $data = $request->validated();
        $chartData = $this->homeService->getChart($data);

        return response()->json($chartData);
    }
}
