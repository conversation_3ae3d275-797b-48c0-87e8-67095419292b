<?php

namespace App\Http\Controllers;

use App\Repositories\Contract\CityRepositoryInterface;
use App\Repositories\Contract\UserAppRepositoryInterface;
use App\Services\NotificationService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    protected NotificationService $notificationService;

    protected CityRepositoryInterface $cityRepository;

    protected UserAppRepositoryInterface $userAppRepository;
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        NotificationService $notificationService,
        CityRepositoryInterface $cityRepository,
        UserAppRepositoryInterface $userAppRepository
    )
    {
        $this->notificationService = $notificationService;
        $this->cityRepository = $cityRepository;
        $this->userAppRepository = $userAppRepository;
    }

    public function index()
    {
        $data = $this->notificationService->getData();

        return view('notification.index', [
            'data' => $data
        ]);
    }

    public function create()
    {
        $portalType = config('vikingapp.portal_type');
        $notifyType = config('vikingapp.type');
        $gender = config('vikingapp.gender');
        $postalCode = $this->cityRepository->pluck('PostalCode')->mapWithKeys(function ($item) {
            return [$item => $item];
        })->toArray();

        return view('notification.create', [
            'portalType' => $portalType,
            'notifyType' => $notifyType,
            'gender' => $gender,
            'postalCode' => $postalCode
        ]);
    }

    public function store(Request $request)
    {
        $data = $request->all();
        $data['StartDate'] = Carbon::createFromFormat('d/m/Y', $data['StartDate'])->format('Y-m-d');
        $data['EndDate'] = Carbon::createFromFormat('d/m/Y', $data['EndDate'])->format('Y-m-d');
        $data['City'] = !empty($data['City']) ? implode(';', $data['City']) : null;
        $data['ZipCode'] = !empty($data['ZipCode']) ?implode(';', $data['ZipCode']) : null;
        $data['Body'] = html_entity_decode($data['Body'], ENT_QUOTES, 'UTF-8');

        if (!empty($data['All_Ages']) && $data['All_Ages'] == 'All') {
            $data['FromAge'] = null;
            $data['ToAge'] = null;
        }

        $response = $this->notificationService->store($data);

        return redirect()->route('notification.index')->with(['success' => $response['message']]);
    }

    public function getUserIdByPortalType(Request $request): JsonResponse
    {
        $portalType = $request->get('portal_type');
        $data = $this->userAppRepository->getUserIdByPortalType($portalType);

        return response()->json($data);
    }

    public function getCitiesByKeyword(Request $request): JsonResponse
    {
        $keyword = $request->get('keyword');
        $data = $this->cityRepository->getCitiesByKeyword($keyword);

        return response()->json($data);
    }

    public function getPostalCodeByCities(Request $request): JsonResponse
    {
        $cities = $request->get('cities');
        $data = $this->cityRepository->getPostalCodeByCities($cities);

        return response()->json($data);
    }

    public function getZipcodeByKeyword(Request $request): JsonResponse
    {
        $keyword = $request->get('keyword');
        $data = $this->cityRepository->getZipcodeByKeyword($keyword);

        return response()->json($data);
    }
}
