<?php

namespace App\Http\Controllers;

use App\Services\Api\VikingStoreSystemService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

/**
 * StoreScheduleController
 *
 * Controller for handling store schedule management functionality.
 * This is separate from general store details and focuses specifically on schedule operations.
 */
class StoreScheduleController extends Controller
{
    protected VikingStoreSystemService $vikingStoreSystemService;

    /**
     * Create a new controller instance.
     *
     * @param VikingStoreSystemService $vikingStoreSystemService
     * @return void
     */
    public function __construct(VikingStoreSystemService $vikingStoreSystemService)
    {
        $this->vikingStoreSystemService = $vikingStoreSystemService;
    }

    /**
     * Display the store schedules management interface
     *
     * @return View
     */
    public function index(): View
    {
        // Get data using the three required service methods
        $storeDetail = $this->vikingStoreSystemService->getStoreDetail(null); // Get all stores
        $storeServices = $this->vikingStoreSystemService->getStoreServices(null); // Get all services
        $metaData = $this->vikingStoreSystemService->getStoreMasterData();

        return view('store.schedules.index', [
            'storeDetail' => $storeDetail,
            'storeServices' => $storeServices,
            'metaData' => $metaData
        ]);
    }

    /**
     * Display schedules for a specific store
     *
     * @param string $id
     * @return View
     */
    public function show(string $id): View
    {
        // Get data using the three required service methods for specific store
        $storeDetail = $this->vikingStoreSystemService->getStoreDetail($id);
        $storeServices = $this->vikingStoreSystemService->getStoreServices($id);
        $metaData = $this->vikingStoreSystemService->getStoreMasterData();

        return view('store.schedules.show', [
            'storeDetail' => $storeDetail,
            'storeServices' => $storeServices,
            'metaData' => $metaData,
            'storeId' => $id
        ]);
    }

    /**
     * Update store schedules
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function update(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'schedules' => 'required|array',
            'schedules.*.store_id' => 'required|integer',
            'schedules.*.schedule' => 'required|array',
        ]);

        try {
            // Process schedule updates
            foreach ($validated['schedules'] as $scheduleData) {
                $storeId = $scheduleData['store_id'];
                $schedule = $scheduleData['schedule'];
                
                // Here you would typically call an API endpoint to update schedules
                // For now, we'll simulate the update process
                $this->updateStoreSchedule($storeId, $schedule);
            }

            return response()->json([
                'success' => true,
                'message' => 'Store schedules updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update store schedules: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk update multiple store schedules
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'store_ids' => 'required|array',
            'store_ids.*' => 'integer',
            'schedule_template' => 'required|array',
        ]);

        try {
            foreach ($validated['store_ids'] as $storeId) {
                $this->updateStoreSchedule($storeId, $validated['schedule_template']);
            }

            return response()->json([
                'success' => true,
                'message' => 'Bulk schedule update completed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to bulk update schedules: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get filtered schedule data
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getFilteredSchedules(Request $request): JsonResponse
    {
        $filters = $request->validate([
            'search' => 'nullable|string',
            'category' => 'nullable|string',
            'city' => 'nullable|string',
            'schedule_status' => 'nullable|string',
        ]);

        try {
            // Get all store data
            $storeDetail = $this->vikingStoreSystemService->getStoreDetail(null);
            $metaData = $this->vikingStoreSystemService->getStoreMasterData();

            // Apply filters (this would typically be done in a service layer)
            $filteredData = $this->applyFilters($storeDetail, $filters);

            return response()->json([
                'success' => true,
                'data' => $filteredData,
                'metaData' => $metaData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get filtered schedules: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Private method to update a store's schedule
     *
     * @param int $storeId
     * @param array $schedule
     * @return bool
     */
    private function updateStoreSchedule(int $storeId, array $schedule): bool
    {
        // This would typically make an API call to update the schedule
        // For now, we'll simulate the process
        
        // Validate schedule format
        $this->validateScheduleFormat($schedule);
        
        // Here you would call the appropriate API endpoint
        // Example: $this->vikingStoreSystemService->updateStoreSchedule($storeId, $schedule);
        
        return true;
    }

    /**
     * Validate schedule format
     *
     * @param array $schedule
     * @throws \InvalidArgumentException
     */
    private function validateScheduleFormat(array $schedule): void
    {
        $requiredDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        
        foreach ($requiredDays as $day) {
            if (!isset($schedule[$day])) {
                throw new \InvalidArgumentException("Missing schedule for {$day}");
            }
            
            if (!is_array($schedule[$day])) {
                throw new \InvalidArgumentException("Invalid schedule format for {$day}");
            }
            
            // Validate time periods
            foreach ($schedule[$day] as $period) {
                if (!isset($period['open']) || !isset($period['close'])) {
                    throw new \InvalidArgumentException("Invalid time period format for {$day}");
                }
                
                if (!$this->isValidTime($period['open']) || !$this->isValidTime($period['close'])) {
                    throw new \InvalidArgumentException("Invalid time format for {$day}");
                }
            }
        }
    }

    /**
     * Validate time format (HH:MM)
     *
     * @param string $time
     * @return bool
     */
    private function isValidTime(string $time): bool
    {
        return preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $time);
    }

    /**
     * Apply filters to store data
     *
     * @param array $storeData
     * @param array $filters
     * @return array
     */
    private function applyFilters(array $storeData, array $filters): array
    {
        // This would typically be implemented in a service layer
        // For now, return the data as-is
        return $storeData;
    }
}
