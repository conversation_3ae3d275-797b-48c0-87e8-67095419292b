<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */

    public function rules()
    {
        return [
            'name' => 'nullable|string',
            'email' => 'nullable|string',
            'city' => 'nullable|string',
            'subscription_plan' => 'nullable|string',
            'status' => 'nullable|string',
            'category' => 'nullable|string',
        ];
    }
}
