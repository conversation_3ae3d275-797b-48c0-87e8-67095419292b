<?php

namespace App\Repositories\Contract;

interface BaseRepositoryInterface
{
    /**
     * Get empty model.
     * @param $model
     */
    public function getModel($model);

    /*
     * Make a new instance of the entity to query on.
     *
     * @param array $with
     */
    public function make(array $with = []);

    /**
     * Get table name.
     *
     * @return string
     */
    public function getTable(): string;

    /**
     * @param int $paginate
     * @param array $columns
     * @return mixed
     */
    public function all(int $paginate = 20, array $columns = ['*']);

    /**
     * @param array $condition
     * @param array $with
     * @param array $select
     * @return mixed
     */
    public function allBy(array $condition, array $with = [], array $select = ['*']);

    /**
     * @param $id
     * @param array $columns
     * @return mixed
     */
    public function find($id, array $columns = ['*']);

    /**
     * @param $id
     * @param array $with
     * @return mixed
     */
    public function findOrFail($id, array $with = []);

    /**
     * Retrieve model by id regardless of status.
     *
     * @param $id
     * @param array $with
     * @return mixed
     */
    public function findById($id, array $with = []);

    /**
     * @param $field
     * @param $value
     * @param array $columns
     * @return mixed
     */
    public function findByField($field, $value, array $columns = ['*']);

    /**
     * @param array $where
     * @param int $paginate
     * @param array $columns
     * @return mixed
     */
    public function findWhere(array $where, int $paginate = 20, array $columns = ['*']);

    /**
     * @param $data
     * @param $condition
     * @return mixed
     */
    public function createOrUpdate($data, $condition);

    /**
     * Find a single entity by key value.
     *
     * @param array $condition
     * @param array $select
     * @param array $with
     * @return mixed
     */
    public function getFirstBy(array $condition = [], array $select = [], array $with = []);

    /**
     * @param $id
     * @return mixed
     */
    public function destroy($id);

    /**
     * @param $id
     * @return mixed
     */
    public function forceDelete($id);

    /**
     * @param int $perPage
     * @param array $columns
     * @return mixed
     */
    public function paginate(int $perPage = 15, $columns = ['*']);
}
