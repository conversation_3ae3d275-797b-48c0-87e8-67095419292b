<?php

namespace App\Repositories\Eloquent;

use App\Models\City;
use App\Repositories\Contract\CityRepositoryInterface;

class EloquentCityRepository extends EloquentBaseRepository implements CityRepositoryInterface
{
    public function model(): string
    {
        return City::class;
    }

    public function getCitiesByKeyword($keyword)
    {
        return $this->model
            ->where(function ($query) use ($keyword) {
                if ($keyword !== null) {
                    $query->where('City', 'like', '%' . $keyword . '%');
                }
            })
            ->distinct()
            ->pluck('City');
    }

    public function getPostalCodeByCities($cities)
    {
        return $this->model
            ->whereIn('City', $cities)
            ->select('PostalCode', 'City')
            ->get();
    }

    public function getZipcodeByKeyword($keyword)
    {
        return $this->model
            ->where(function ($query) use ($keyword) {
                if ($keyword !== null) {
                    $query->where('PostalCode', 'like', '%' . $keyword . '%');
                }
            })
            ->distinct()
            ->pluck('PostalCode');
    }
}
