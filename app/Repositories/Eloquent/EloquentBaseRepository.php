<?php

namespace App\Repositories\Eloquent;

use Illuminate\Container\Container as Application;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Traits\ForwardsCalls;
use App\Repositories\Contract\BaseRepositoryInterface;

/**
 * Class EloquentBaseRepository
 * @package App\Repositories\Eloquent
 * <AUTHOR>
 * @since 1.0
 */
abstract class EloquentBaseRepository implements BaseRepositoryInterface
{
    use ForwardsCalls;

    /**
     * Set default Paginate if it's not defined.
     */
    const DEFAULT_PAGINATE = 10;

    /**
     * @var Model
     */
    protected $model;

    /**
     * @var Application
     */
    protected $app;

    /**
     * EloquentBaseRepository constructor.
     * @param Application $app
     * @throws BindingResolutionException
     */
    public function __construct(Application $app)
    {
        $this->app = $app;
        $this->makeModel();
    }

    /**
     * Handle dynamic method calls into the model.
     *
     * @param string $method
     * @param array $parameters
     * @return mixed
     */
    public function __call(string $method, array $parameters)
    {
        return $this->forwardCallTo($this->model->newQuery(), $method, $parameters);
    }

    /**
     * @throws BindingResolutionException
     */
    public function resetModel()
    {
        $this->makeModel();
    }

    /**
     * Specify Model class name
     *
     * @return string
     */
    abstract public function model(): string;

    /**
     * @return Model|object|string
     * @throws BindingResolutionException
     */
    public function makeModel()
    {
        $model = $this->app->make($this->model());

        if (!$model instanceof Model) {
            return 'Error';
        }
        return $this->model = $model;
    }

    /**
     * @param $model
     * @return mixed|object
     * @throws BindingResolutionException
     */
    public function getModel($model): object
    {
        return $this->app->make($model);
    }

    /**
     * @return string
     */
    public function getTable(): string
    {
        return $this->model->getTable();
    }

    /**
     * @param int $paginate
     * @param array $columns
     * @return mixed|void
     */
    public function all($paginate = 20, $columns = ['*'])
    {
        return $this->model->paginate($paginate);
    }

    public function allWithoutPaginate()
    {
        return $this->model->get();
    }

    /**
     * @param array $columns
     * @return mixed|void
     */
    public function get($columns = ['*'])
    {
        return $this->model->all($columns);
    }

    /**
     * {}
     * @param $condition
     * @param $with
     * @param $select
     * @return Builder[]|Collection
     */
    public function allBy($condition, $with = [], $select = ['*'])
    {
        if (!empty($condition)) {
            $this->applyConditions($condition);
        }

        $data = $this->make($with)->select($select);

        return $data->get();
    }

    /**
     * @param array $where
     * @param null|Eloquent|Builder $model
     */
    protected function applyConditions(array $where, &$model = null)
    {
        if (!$model) {
            $newModel = $this->model;
        } else {
            $newModel = $model;
        }
        foreach ($where as $field => $value) {
            if (is_array($value)) {
                list($field, $condition, $val) = $value;
                switch (strtoupper($condition)) {
                    case 'IN':
                        $newModel = $newModel->whereIn($field, $val);
                        break;
                    case 'NOT_IN':
                        $newModel = $newModel->whereNotIn($field, $val);
                        break;
                    default:
                        $newModel = $newModel->where($field, $condition, $val);
                        break;
                }
            } else {
                $newModel = $newModel->where($field, '=', $value);
            }
        }
        if (!$model) {
            $this->model = $newModel;
        } else {
            $model = $newModel;
        }
    }

    /**
     * @param array $with
     * @return Builder|Model
     */
    public function make(array $with = [])
    {
        if (!empty($with)) {
            $this->model = $this->model->with($with);
        }

        return $this->model;
    }

    /**
     * @param $id
     * @param array $columns
     * @return mixed
     */
    public function find($id, $columns = ['*'])
    {
        return $this->model->findOrFail($id, $columns);
    }

    /**
     * @param $id
     * @param array $with
     * @return mixed
     * @throws \Exception
     */
    public function findOrFail($id, array $with = [])
    {
        $data = $this->make($with)->where('id', $id);
        $result = $data->first();
        $this->resetModel();

        if (!empty($result)) {
            return $result;
        }

        throw (new ModelNotFoundException)->setModel(
            get_class($this->model),
            $id
        );
    }

    /**
     * @param $id
     * @param array $with
     * @return Builder|Model|object|null
     * @throws \Exception
     */
    public function findById($id, array $with = [])
    {
        $data = $this->make($with)->where('id', $id);
        $data = $data->first();

        $this->resetModel();

        return $data;
    }

    /**
     * @param $field
     * @param $value
     * @param bool $allowFail
     * @param array $columns
     * @return mixed
     */
    public function findByField($field, $value, $allowFail = true, $columns = ['*'])
    {
        if ($allowFail) {
            return $this->model->where($field, $value)->select($columns)->firstOrFail();
        }
        return $this->model->where($field, $value)->select($columns)->first();
    }

    /**
     * Update or Create an entity in repository
     *
     * @param array $attributes
     * @param array $values
     * @return mixed
     * <AUTHOR>
     */
    public function updateOrCreate(array $attributes, array $values = [])
    {
        return $this->model->updateOrCreate($attributes, $values);
    }

    /**
     * @param $data
     * @param array $condition
     * @return bool|Model|mixed
     * @throws BindingResolutionException
     */
    public function createOrUpdate($data, $condition = [])
    {
        /**
         * @var Model $item
         */
        if (is_array($data)) {
            if (empty($condition)) {
                $item = new $this->model;
            } else {
                $item = $this->find($condition);
            }
            if (empty($item)) {
                $item = new $this->model;
            }

            $item = $item->fill($data);
        } elseif ($data instanceof Model) {
            $item = $data;
        } else {
            return false;
        }

        if ($item->save()) {
            $this->resetModel();
            return $item;
        }

        $this->resetModel();

        return false;
    }

    /**
     * @param array $condition
     * @param array $select
     * @param array $with
     * @return mixed
     * @throws \Exception
     */
    public function getFirstBy(array $condition = [], array $select = ['*'], array $with = [])
    {
        $this->make($with);

        if (!empty($select)) {
            $data = $this->model->where($condition)->select($select);
        } else {
            $data = $this->model->where($condition);
        }

        return $data->first();
    }

    /**
     * @param array $where
     * @param int $paginate
     * @param array $columns
     * @return mixed
     * @throws BindingResolutionException
     */
    public function findWhere(array $where, $paginate = 0, $columns = ['*'])
    {
        foreach ($where as $field => $value) {
            if (is_array($value)) {
                list($field, $condition, $val) = $value;
                $this->model = $this->model->where($field, $condition, $val);
            } else {
                $this->model = $this->model->where($field, $value);
            }
        }
        if (!$paginate) {
            $model = $this->model->get($columns);
        } else {
            $model = $this->model->paginate($paginate);
        }

        $this->resetModel();
        return $model;
    }

    /**
     * @param array $where
     * @param array $columns
     * @return mixed
     * @throws BindingResolutionException
     */
    public function findWhereFirst(array $where, $columns = ['*'])
    {
        foreach ($where as $field => $value) {
            if (is_array($value)) {
                list($field, $condition, $val) = $value;
                $this->model = $this->model->where($field, $condition, $val);
            } else {
                $this->model = $this->model->where($field, $value);
            }
        }
        $model = $this->model->first($columns);
        $this->resetModel();
        return $model;
    }

    /**
     * @param $column
     * @param string $direction
     * @return $this
     */
    public function orderBy($column, $direction = 'asc'): EloquentBaseRepository
    {
        $this->model = $this->model->orderBy($column, $direction);
        return $this;
    }

    /**
     * @param $relations
     * @return $this
     */
    public function with($relations): EloquentBaseRepository
    {
        $this->model = $this->model->with($relations);

        return $this;
    }

    /**
     * Load relations
     *
     * @return $this
     */
    public function withTrashed(): EloquentBaseRepository
    {
        $this->model = $this->model->withTrashed();

        return $this;
    }

    /**
     * Load relations
     *
     * @return $this
     */
    public function onlyTrashed(): EloquentBaseRepository
    {
        $this->model = $this->model->onlyTrashed();

        return $this;
    }

    /**
     * @param $id
     * @return mixed
     */
    public function destroy($id): bool
    {
        $post = $this->find($id);
        $post->delete();
        return true;
    }

    /**
     * @param $id
     * @return bool|mixed
     */
    public function forceDelete($id): bool
    {
        $post = $this->onlyTrashed()->find($id);
        $post->forceDelete();
        return true;
    }

    /**
     * Update a entity in repository by id
     *
     * @param array $attributes
     * @param       $id
     *
     * @return mixed
     * @throws \Exception
     *
     */
    public function update(array $attributes, $id)
    {
        $model = $this->findOrFail($id);
        $model->fill($attributes);
        $model->save();
        return $model;
    }

    /**
     * @param int $perPage
     * @param array $columns
     * @return mixed
     */
    public function paginate($perPage = 5, $columns = ['*'])
    {
        return $this->model->paginate($perPage, $columns);
    }

    /**
     * Load relation with closure
     *
     * @param $relation
     * @param $closure
     *
     * @return $this
     */
    public function whereHas($relation, $closure): EloquentBaseRepository
    {
        $this->model = $this->model->whereHas($relation, $closure);
        return $this;
    }

    /**
     * {}
     * @param Model $model
     * @return bool|null
     * @throws \Exception
     */
    public function delete(Model $model): ?bool
    {
        return $model->delete();
    }

    /**
     * @param array $condition
     * @return mixed
     * @throws \Exception
     */
    public function exists(array $condition = [])
    {
        return $this->model->where($condition)->exists();
    }

    /**
     * @param $name
     * @param $key
     * @return mixed
     * @throws \Exception
     */
    public function pluck($name, $key = '', $where = [])
    {
        if (empty($key)) {
            return $this->model->where($where)->pluck($name);
        }
        return $this->model->where($where)->pluck($name, $key);
    }

    /**
     * {}
     * @param $where
     * @return bool|null
     * @throws \Exception
     */
    public function whereDelete($where): ?bool
    {
        return $this->model->where($where)->delete();
    }
}
