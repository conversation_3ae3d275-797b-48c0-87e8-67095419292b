<?php

namespace App\Repositories\Eloquent;

use App\Enums\AppType;
use App\Repositories\Contract\UserAppRepositoryInterface;
use App\Models\UserApp;

class EloquentUserAppRepository extends EloquentBaseRepository implements UserAppRepositoryInterface
{
    public function model(): string
    {
        return UserApp::class;
    }

    public function getTotalUserByApp($typeApp, $conditions = [])
    {
        return $this->model
            ->selectRaw('COUNT(id) as total')
            ->where($conditions)
            ->where(function ($query) use ($typeApp) {
                if ($typeApp !== AppType::ALL) {
                    $query->where('AreaId', $typeApp);
                }
            })
            ->first();
    }

    public function getTotalUserAppByDay($typeApp, $conditions = [])
    {
        return $this->model
            ->selectRaw('DATE(CreatedDate) as date, COUNT(*) AS total')
            ->where('AreaId', $typeApp)
            ->where($conditions)
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }

    public function getTotalUserAppByMonth($typeApp, $conditions = [])
    {
        return $this->model
            ->selectRaw('YEAR(CreatedDate) as year, DATE_FORMAT(CreatedDate, "%m") AS month, COUNT(*) as total')
            ->where('AreaId', $typeApp)
            ->where($conditions)
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();
    }

    public function getUserIdByPortalType($portalType)
    {
        return $this->model
            ->where(function ($query) use ($portalType) {
                if ($portalType !== null) {
                    $query->where('AreaId', config('vikingapp.user')[$portalType]);
                }
            })->pluck('Id');
    }
}
