<?php

namespace App\Providers;

use App\Repositories\Contract\CityRepositoryInterface;
use App\Repositories\Contract\NotificationRepositoryInterface;
use App\Repositories\Contract\SpecificHelpRepositoryInterface;
use App\Repositories\Contract\StoreRepositoryInterface;
use App\Repositories\Contract\UserAppRepositoryInterface;
use App\Repositories\Eloquent\EloquentCityRepository;
use App\Repositories\Eloquent\EloquentNotificationRepository;
use App\Repositories\Eloquent\EloquentSpecificHelpRepository;
use App\Repositories\Eloquent\EloquentStoreRepository;
use App\Repositories\Eloquent\EloquentUserAppRepository;
use Illuminate\Support\Facades\URL;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(
            UserAppRepositoryInterface::class,
            EloquentUserAppRepository::class
        );

        $this->app->singleton(
            NotificationRepositoryInterface::class,
            EloquentNotificationRepository::class
        );

        $this->app->singleton(
            CityRepositoryInterface::class,
            EloquentCityRepository::class
        );

        $this->app->singleton(
            SpecificHelpRepositoryInterface::class,
            EloquentSpecificHelpRepository::class
        );

        $this->app->singleton(
            StoreRepositoryInterface::class,
            EloquentStoreRepository::class
        );
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if (config('app.env') !== 'local') {
            URL::forceScheme('https');
        }
        Paginator::useBootstrapFive();
    }
}
