<?php

namespace App\Jobs;

use App\Models\Import;
use App\Services\Api\VikingStoreSystemService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class IntegrateImportStoreJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    protected $importId;

    protected $chunkPath;

    protected $seq;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($importId, $chunkPath, $seq)
    {
        $this->importId = $importId;
        $this->chunkPath = $chunkPath;
        $this->seq = $seq;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(VikingStoreSystemService $vikingStoreSystemService)
    {
        $import = Import::findOrFail($this->importId);

        $body[] = [
            'name' => 'File',
            'contents' => fopen(storage_path('app/public/' . $this->chunkPath), 'r'),
            'filename' => "part-{$this->seq}.csv"
        ];

        $response = $vikingStoreSystemService->import($body);
        $response = [
            'status' => 200,
            'body' => '{"success":false,"data":[{"index":2,"invalidFields":"email exists"},{"index":3,"invalidFields":"name invalid"}]}'
        ];
        if ($response['status'] == ResponseAlias::HTTP_OK) {
            $body = json_decode($response['body']);
            if (!$body->success) {
                $invalid = $body->data;
                \DB::transaction(function () use ($import, $invalid) {
                    $import->lockForUpdate();
                    $current = $import->invalid_row ?? [];
                    $import->invalid_row = array_merge($current, $invalid);
                    $import->invalid_count = count($import->invalid_row);
                    $import->save();
                });
            }
        }
    }
}
