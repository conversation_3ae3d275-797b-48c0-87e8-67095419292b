<?php

namespace App\Jobs;

use App\Models\Import;
use App\Services\Api\VikingStoreSystemService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class IntegrateImportStoreJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    protected $importId;

    protected $filePath;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($importId, $filePath)
    {
        $this->importId = $importId;
        $this->filePath = $filePath;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(VikingStoreSystemService $vikingStoreSystemService)
    {
        $import = Import::findOrFail($this->importId);

        $body[] = [
            'name' => 'File',
            'contents' => fopen(storage_path('app/public/' . $this->filePath), 'r')
        ];

        $response = $vikingStoreSystemService->import($body);
        if ($response['status'] == ResponseAlias::HTTP_OK) {
            $body = json_decode($response['body']);
            if (!$body->success) {
                $invalid = $body->data->rows;
                $import->update([
                    'status' => Import::STATUS_COMPLETED,
                    'invalid_count' => count($invalid),
                    'invalid_row'=> $invalid,
                ]);
            }
        }
    }
}
