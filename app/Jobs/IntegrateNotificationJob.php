<?php

namespace App\Jobs;

use App\Services\Api\VikingNotificationSystemService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;

class IntegrateNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $data;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(VikingNotificationSystemService $vikingNotificationSystemService)
    {
        $body = [];

        if (isset($this->data['Image']) && Storage::exists($this->data['Image'])) {
            $this->data['Image'] = fopen(storage_path('app/public/' . $this->data['Image']), 'r');
        }

        foreach ($this->data as $key => $item) {
            if ($key == 'UserIds') {
                foreach ($item as $userId) {
                    $body[] = [
                        'name' => 'UserIds',
                        'contents' => $userId
                    ];
                }
                continue;
            }
            $body[] = [
                'name' => $key,
                'contents' => is_array($item) ? json_encode($item) : $item
            ];
        }

        $response = $vikingNotificationSystemService->triggerNotify($body);
        \Log::info($response);
    }
}
