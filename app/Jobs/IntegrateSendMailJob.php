<?php

namespace App\Jobs;

use App\Services\Api\VikingMailSystemService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class IntegrateSendMailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $data;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(VikingMailSystemService $vikingMailSystemService)
    {
        $body = [
            'recipients' => [
                [
                    'email' => $this->data['emailTo'],
                    'name' => $this->data['username']
                ]
            ],
            'body' => $this->data['body'],
            'subject' => $this->data['subject']
        ];

        $vikingMailSystemService->sendMail($body);
    }
}
