<?php

namespace App\Services\Api;

use App\Services\API;
use GuzzleHttp\Psr7\MultipartStream;

class VikingStoreSystemService
{
    /**
     * @var API
     */
    protected $API;

    protected $domain;

    protected $headers = [];

    public function __construct(API $API)
    {
        $this->API = $API;
        $this->domain = config('vikingapp.api.domain');
        $this->headers = [
            'x-api-key' => config('vikingapp.api.secret_key')
        ];
    }

    /**
     * @return mixed
     */
    public function import($body)
    {
        $endpoint = config('vikingapp.api.import');
        $api = $this->domain . $endpoint;

        return $this->API->sendAsync($api, 'POST', $this->headers, new MultipartStream($body));
    }

    /**
     * @return mixed
     */
    public function getStoreInfo($id)
    {
        $api = $this->buildApiUrl('store-info', $id);

        return $this->API->sendAsync($api, 'GET', $this->headers);
    }

    public function getStoreMasterData()
    {
        $api = $this->buildApiUrl('store-master-data');
        $response = $this->API->sendAsync($api, 'GET', $this->headers);

        if (isset($response['body'])) {
            $responseData = json_decode($response['body'], true);

            if (isset($responseData['success']) &&
                $responseData['success'] === true &&
                isset($responseData['data'])) {
                return $responseData['data'];
            }
        }

        return $response;
    }

    public function getStoreDetail($id)
    {
        $api = $this->buildApiUrl('store-detail', $id);
        $response = $this->API->sendAsync($api, 'GET', $this->headers);

        if (isset($response['body'])) {
            $responseData = json_decode($response['body'], true);

            if (isset($responseData['success']) &&
                $responseData['success'] === true &&
                isset($responseData['data'])) {
                return $responseData['data'];
            }
        }

        return $response;
    }

    public function getStoreServices($id)
    {
        $api = $this->buildApiUrl('store-service', $id);
        $response = $this->API->sendAsync($api, 'GET', $this->headers);

        if (isset($response['body'])) {
            $responseData = json_decode($response['body'], true);

            if (isset($responseData['success']) &&
                $responseData['success'] === true &&
                isset($responseData['data'])) {
                return $responseData['data'];
            }
        }

        return $response;
    }

    private function buildApiUrl($endpoint, $id = null): array|string
    {
        $url = config('vikingapp.api.domain') . config("vikingapp.api.$endpoint");

        if ($id && strpos($url, '{id}') !== false) {
            $url = str_replace('{id}', $id, $url);
        }

        return $url;
    }
}
