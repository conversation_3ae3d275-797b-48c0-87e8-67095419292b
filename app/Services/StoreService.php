<?php

namespace App\Services;
use App\Enums\SubscriptionType;
use App\Enums\SubscriptionLevel;
use App\Jobs\IntegrateImportStoreJob;
use App\Models\AddressSchedule;
use App\Models\Category;
use App\Models\StoreCategory;
use App\Services\Api\VikingStoreSystemService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Models\Import;
use SplFileObject;
use Illuminate\Support\Facades\Bus;
use App\Enums\StoreStatus;
use App\Repositories\Contract\StoreRepositoryInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;

/**
 * StoreService
 *
 * Service class for handling store-related business logic.
 */
class StoreService
{
    const CHUNK_SIZE = 2;
    protected string $folder = 'import';
    protected BaseService $baseService;
    protected VikingStoreSystemService $vikingStoreSystemService;
    protected StoreRepositoryInterface $storeRepository;

    public function __construct(
        BaseService $baseService,
        VikingStoreSystemService $vikingStoreSystemService,
        StoreRepositoryInterface $storeRepository
    )
    {
        $this->baseService = $baseService;
        $this->vikingStoreSystemService = $vikingStoreSystemService;
        $this->storeRepository = $storeRepository;
    }

    public function getData(string $type = null, array $filters = []): LengthAwarePaginator
    {
        $query = $this->storeRepository->getQuery()
            ->with(['subscription', 'subscriptionPlan', 'address', 'employee', 'business', 'categories']);

        $this->baseService->applyTypeFilter($query, $type);

        $this->applyFormFilters($query, $filters);

        $stores = $query->orderBy('CreatedDate', 'DESC')->paginate(10);

        $stores->getCollection()->transform(function ($store) {
            $store->main_categories = $store->getMainCategories();
            return $store;
        });

        return $stores;
    }

    /**
     * Apply form filters to the query.
     *
     * @param Builder $query
     * @param array $filters
     * @return void
     */
    private function applyFormFilters(Builder $query, array $filters): void
    {
        if (!empty($filters['name'])) {
            $query->where('Name', 'like', '%' . $filters['name'] . '%');
        }

        if (!empty($filters['email'])) {
            $query->whereHas('employee', function($q) use ($filters) {
                $q->where('Email', 'like', '%' . $filters['email'] . '%');
            });
        }

        if (!empty($filters['city'])) {
            $query->whereHas('address', function($q) use ($filters) {
                $q->where('City', $filters['city']);
            });
        }

        if (!empty($filters['subscription_plan'])) {
            $query->whereHas('subscriptionPlan', function($q) use ($filters) {
                $q->where('Name', $filters['subscription_plan']);
            });
        }

        if (!empty($filters['category'])) {
            $query->whereHas('categories', function($q) use ($filters) {
                $q->where('MainCategoryName', $filters['category']);
            });
        }

        if (isset($filters['status']) && $filters['status'] !== '') {
            $query->where('Status', (int) $filters['status']);
        }
    }

    /**
     * Get unique values from a relationship field.
     *
     * @param string $relation
     * @param string $field
     * @return array
     */
    public function getUniqueValues(string $relation, string $field): array
    {
        return $this->storeRepository->getQuery()
            ->with($relation)
            ->get()
            ->pluck("$relation.$field")
            ->unique()
            ->filter()
            ->values()
            ->toArray();
    }

    /**
     * Update the status of a store.
     *
     * @param string $id The store ID
     * @param int|StoreStatus $status
     * @return bool
     */
    public function updateStatus(string $id, int|StoreStatus $status): bool
    {
        try {
            $statusValue = $status instanceof StoreStatus ? $status->value : $status;

            $this->storeRepository->where('Id', $id)
                ->update(['Status' => $statusValue]);
            return true;
        } catch (\Exception $e) {
            Log::error('Error updating store status: ' . $e->getMessage());
            return false;
        }
    }

    public function import($file)
    {
        $user = Auth::user();
        $fileName = $file->getClientOriginalName();

        $import = Import::create([
            'user_id' => $user->id,
            'original_name' => $fileName,
        ]);

        $csv = new SplFileObject($file->getRealPath());

        $csv->setFlags(
            SplFileObject::READ_CSV
            | SplFileObject::SKIP_EMPTY
            | SplFileObject::READ_AHEAD
        );

        $header = $csv->current();
        $header[] = 'line';
        $jobs = [];
        $chunk = 0;
        $rowInChunk = 0;
        $handle = null;
        $chunkPath = null;

        foreach ($csv as $rowIndex => $row) {
            if ($rowIndex === 0) continue;

            if ($rowInChunk === 0) {
                [$handle, $chunkPath] = $this->openChunk($import->id, $chunk, $header);
            }

            $row[] = $rowIndex;
            fputcsv($handle, $row);
            $rowInChunk++;

            if ($rowInChunk === self::CHUNK_SIZE) {
                fclose($handle);
                $jobs[] = new IntegrateImportStoreJob($import->id, $chunkPath, $chunk);

                $chunk++;
                $rowInChunk = 0;
                $handle = null;
                $chunkPath = null;
            }
        }

        if ($rowInChunk > 0) {
            fclose($handle);
            $jobs[] = new IntegrateImportStoreJob($import->id, $chunkPath, $chunk);
        }

        $batch = Bus::batch($jobs)
            ->name("Import #{$import->id}")
            ->then(function () use ($import) {
                $import->status = Import::STATUS_COMPLETED;
                $import->save();
            })
            ->catch(function () use ($import) {
                $import->status = Import::STATUS_FAILED;
                $import->save();
            })
            ->finally(function () use ($import) {
                Storage::deleteDirectory("$this->folder/$import->id");
            })
            ->dispatch();

        $import->update(['batch_id' => $batch->id]);

        return [
            'message' => 'Store data imported successfully!',
            'data' => $import
        ];
    }

    private function openChunk(int $importId, int $idx, array $header)
    {
        $dir = "$this->folder/$importId";
        Storage::makeDirectory($dir);

        $chunkPath = "{$dir}/part-{$idx}.csv";
        $localPath = Storage::path($chunkPath);
        $handle = fopen($localPath, 'w');
        fputcsv($handle, $header);

        return [$handle, $chunkPath];
    }

    public function processImport($import)
    {
        if ($import->status === Import::STATUS_PROCESSING) {
            $message = "Importing is processing";
        }
        elseif ($import->status === Import::STATUS_FAILED) {
            $message = "Importing is failed";
        } else {
            $message = "Importing is completed";
        }

        return [
            'message' => $message,
            'data' => [
                'user_id' => $import->user_id,
                'status' => $import->status,
                'original_name' => $import->original_name,
                'invalid_row' => $import->invalid_row,
                'created_at' => $import->created_at
            ]
        ];
    }

    public function getFilterOptions(): array
    {
        return [
            'categories' => $this->getUniqueValues('category', 'MainCategoryName'),
            'cities' => $this->getUniqueValues('address', 'City'),
            'subscriptionPlans' => $this->getUniqueValues('subscriptionPlan', 'Name')
        ];
    }

    public function getFilteredData(?string $type, array $filters, $request): LengthAwarePaginator
    {
        $data = $this->getData($type, $filters);
        $data->appends($request->all());
        return $data;
    }

    /**
     * Get store with all related data for the detail page.
     *
     * @param string $id
     * @return mixed
     */
    public function getStoreWithAllRelations(string $id)
    {
        return $this->storeRepository->getQuery()
            ->with([
                'subscription',
                'subscriptionPlan',
                'address.schedule',
                'employee',
                'business',
                'categories'
            ])
            ->where('Id', $id)
            ->first();
    }
}
