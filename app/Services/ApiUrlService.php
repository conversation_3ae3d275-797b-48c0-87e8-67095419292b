<?php
namespace App\Services;

class ApiUrlService
{
    /**
     * Build API URL from config with dynamic ID replacement
     *
     * @param string $endpoint The endpoint key from config/vikingapp.php
     * @param int|null $id The ID to replace {id} placeholder
     * @return string
     */
    public static function build($endpoint, $id = null)
    {
        $domain = config('vikingapp.api.domain');
        $path = config("vikingapp.api.$endpoint");
        if (!$path) {
            throw new \InvalidArgumentException("API endpoint '$endpoint' not found in configuration");
        }
        $url = rtrim($domain, '/') . '/' . ltrim($path, '/');
        if ($id && strpos($url, '{id}') !== false) {
            $url = str_replace('{id}', $id, $url);
        }
        return $url;
    }
    /**
     * Get all available API endpoints
     *
     * @return array
     */
    public static function getAllEndpoints()
    {
        return config('vikingapp.api');
    }
    /**
     * Build multiple URLs at once
     *
     * @param array $endpoints Array of endpoint configurations
     * @return array
     */
    public static function buildMultiple(array $endpoints)
    {
        $urls = [];
        foreach ($endpoints as $key => $config) {
            if (is_array($config) && isset($config['endpoint'])) {
                $urls[$key] = self::build($config['endpoint'], $config['id'] ?? null);
            } else {
                $urls[$key] = self::build($config);
            }
        }
        return $urls;
    }
    /**
     * Store-specific helper methods
     */
    public static function getStoreInfo($id)
    {
        return self::build('store-info', $id);
    }
    public static function getStoreDetail($id)
    {
        return self::build('store-detail', $id);
    }
    public static function getStoreMasterData()
    {
        return self::build('store-master-data');
    }
    public static function getStoreCategories($id)
    {
        return self::build('store-categories', $id);
    }
    /**
     * Get all store-related URLs for a specific store ID
     *
     * @param int $storeId
     * @return array
     */
    public static function getAllStoreUrls($storeId)
    {
        return [
            'info' => self::getStoreInfo($storeId),
            'detail' => self::getStoreDetail($storeId),
            'master_data' => self::getStoreMasterData(),
            'categories' => self::getStoreCategories($storeId),
        ];
    }
}
