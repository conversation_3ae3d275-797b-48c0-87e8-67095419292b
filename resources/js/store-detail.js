/**
 * Store Schedule Manager
 * Handles all functionality for the store schedule management page
 */
class StoreScheduleManager {
    constructor() {
        this.storeData = null;
        this.servicesData = null;
        this.metaData = null;
        this.scheduleData = [];
        this.selectedStores = new Set();
        this.filters = {
            search: '',
            category: '',
            city: '',
            schedule: ''
        };
        this.init();
    }

    init() {
        this.loadData();
        this.bindEvents();
        this.initializeFilters();
        this.renderScheduleTable();
    }

    loadData() {
        // Get data from window object (passed from Blade template)
        this.storeData = window.storeDetail || null;
        this.servicesData = window.storeServices || null;
        this.metaData = window.metaData || null;

        console.log('Store Data:', this.storeData);
        console.log('Services Data:', this.servicesData);
        console.log('Meta Data:', this.metaData);

        // Generate mock schedule data for demonstration
        this.generateMockScheduleData();
    }

    generateMockScheduleData() {
        // Mock data based on the UI design
        this.scheduleData = [
            {
                id: 1,
                name: 'Downtown Market',
                category: 'Grocery',
                city: 'New York',
                schedule: {
                    monday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '20:00' }],
                    tuesday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '20:00' }],
                    wednesday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '20:00' }],
                    thursday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '20:00' }],
                    friday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '20:00' }],
                    saturday: [{ open: '09:00', close: '12:30' }, { open: '13:30', close: '19:00' }],
                    sunday: []
                }
            },
            {
                id: 2,
                name: 'Westside Electronics',
                category: 'Electronics',
                city: 'Los Angeles',
                schedule: {
                    monday: [{ open: '09:00', close: '12:30' }, { open: '13:30', close: '19:00' }],
                    tuesday: [{ open: '09:00', close: '12:30' }, { open: '13:30', close: '19:00' }],
                    wednesday: [{ open: '09:00', close: '12:30' }, { open: '13:30', close: '19:00' }],
                    thursday: [{ open: '09:00', close: '12:30' }, { open: '13:30', close: '19:00' }],
                    friday: [{ open: '09:00', close: '12:30' }, { open: '13:30', close: '19:00' }],
                    saturday: [{ open: '09:00', close: '12:30' }, { open: '13:30', close: '19:00' }],
                    sunday: []
                }
            },
            {
                id: 3,
                name: 'Northside Pharmacy',
                category: 'Pharmacy',
                city: 'Chicago',
                schedule: {
                    monday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '18:00' }],
                    tuesday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '18:00' }],
                    wednesday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '18:00' }],
                    thursday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '18:00' }],
                    friday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '18:00' }],
                    saturday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '18:00' }],
                    sunday: []
                }
            },
            {
                id: 4,
                name: 'Eastside Cafe',
                category: 'Food & Beverage',
                city: 'Boston',
                schedule: {
                    monday: [{ open: '07:00', close: '20:00' }],
                    tuesday: [{ open: '07:00', close: '20:00' }],
                    wednesday: [{ open: '07:00', close: '20:00' }],
                    thursday: [{ open: '07:00', close: '20:00' }],
                    friday: [{ open: '07:00', close: '20:00' }],
                    saturday: [{ open: '07:00', close: '20:00' }],
                    sunday: [{ open: '10:00', close: '13:00' }, { open: '14:00', close: '19:00' }]
                }
            },
            {
                id: 5,
                name: 'Central Bookstore',
                category: 'Retail',
                city: 'Seattle',
                schedule: {
                    monday: [{ open: '10:00', close: '13:00' }, { open: '14:00', close: '19:00' }],
                    tuesday: [{ open: '10:00', close: '13:00' }, { open: '14:00', close: '19:00' }],
                    wednesday: [{ open: '10:00', close: '13:00' }, { open: '14:00', close: '19:00' }],
                    thursday: [{ open: '10:00', close: '13:00' }, { open: '14:00', close: '19:00' }],
                    friday: [{ open: '10:00', close: '13:00' }, { open: '14:00', close: '19:00' }],
                    saturday: [{ open: '10:00', close: '13:00' }, { open: '14:00', close: '19:00' }],
                    sunday: []
                }
            }
        ];
    }

    bindEvents() {
        // Search functionality
        $('#searchStores').on('input', this.handleSearch.bind(this));
        
        // Filter dropdowns
        $('.dropdown-item').on('click', this.handleFilterChange.bind(this));
        
        // Select all checkbox
        $('#selectAll').on('change', this.handleSelectAll.bind(this));
        
        // Individual store checkboxes
        $(document).on('change', '.store-checkbox', this.handleStoreSelect.bind(this));
        
        // Time input changes
        $(document).on('change', '.time-input', this.handleTimeChange.bind(this));
        
        // Add/remove period buttons
        $(document).on('click', '.add-period-btn', this.handleAddPeriod.bind(this));
        $(document).on('click', '.remove-period-btn', this.handleRemovePeriod.bind(this));
        
        // Action buttons
        $('#saveAllBtn').on('click', this.handleSaveAll.bind(this));
        $('#discardBtn').on('click', this.handleDiscard.bind(this));
        $('#bulkEditBtn').on('click', this.handleBulkEdit.bind(this));
        $('#changeLogBtn').on('click', this.handleChangeLog.bind(this));
    }

    initializeFilters() {
        // Populate filter dropdowns with unique values
        const categories = [...new Set(this.scheduleData.map(store => store.category))];
        const cities = [...new Set(this.scheduleData.map(store => store.city))];

        // Populate category dropdown
        const categoryDropdown = $('#categoryDropdown');
        categories.forEach(category => {
            categoryDropdown.append(`<li><a class="dropdown-item" href="#" data-filter="category" data-value="${category}">${category}</a></li>`);
        });

        // Populate city dropdown
        const cityDropdown = $('#cityDropdown');
        cities.forEach(city => {
            cityDropdown.append(`<li><a class="dropdown-item" href="#" data-filter="city" data-value="${city}">${city}</a></li>`);
        });
    }

    renderScheduleTable() {
        const tbody = $('#scheduleTableBody');
        tbody.empty();

        const filteredData = this.getFilteredData();

        filteredData.forEach(store => {
            const row = this.createStoreRow(store);
            tbody.append(row);
        });

        this.updateBulkEditButton();
    }

    createStoreRow(store) {
        const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        
        let row = `
            <tr class="store-row" data-store-id="${store.id}">
                <td>
                    <input type="checkbox" class="form-check-input store-checkbox" data-store-id="${store.id}">
                </td>
                <td>
                    <div class="store-name">${store.name}</div>
                </td>
                <td>
                    <div class="store-category">${store.category}</div>
                </td>
                <td>
                    <div class="store-city">${store.city}</div>
                </td>
        `;

        days.forEach(day => {
            row += `<td class="schedule-day">${this.createDaySchedule(store, day)}</td>`;
        });

        row += '</tr>';
        return row;
    }

    createDaySchedule(store, day) {
        const daySchedule = store.schedule[day];
        
        if (!daySchedule || daySchedule.length === 0) {
            return '<div class="closed-indicator">Closed</div>';
        }

        let scheduleHtml = '';
        daySchedule.forEach((period, index) => {
            scheduleHtml += `
                <div class="time-slot" data-day="${day}" data-period="${index}">
                    <input type="time" class="time-input" value="${period.open}" data-type="open">
                    <span class="time-separator">-</span>
                    <input type="time" class="time-input" value="${period.close}" data-type="close">
                    ${daySchedule.length > 1 ? '<button class="remove-period-btn" title="Remove period"><i class="fas fa-times"></i></button>' : ''}
                </div>
            `;
        });

        scheduleHtml += `<button class="add-period-btn" data-day="${day}">+ Add second period</button>`;
        
        return scheduleHtml;
    }

    getFilteredData() {
        return this.scheduleData.filter(store => {
            const matchesSearch = !this.filters.search || 
                store.name.toLowerCase().includes(this.filters.search.toLowerCase());
            const matchesCategory = !this.filters.category || store.category === this.filters.category;
            const matchesCity = !this.filters.city || store.city === this.filters.city;
            
            return matchesSearch && matchesCategory && matchesCity;
        });
    }

    // Event Handlers
    handleSearch(event) {
        this.filters.search = event.target.value;
        this.renderScheduleTable();
    }

    handleFilterChange(event) {
        event.preventDefault();
        const filterType = $(event.target).data('filter');
        const filterValue = $(event.target).data('value');
        
        this.filters[filterType] = filterValue;
        
        // Update button text
        const button = $(event.target).closest('.dropdown').find('.dropdown-toggle');
        const icon = button.find('i').prop('outerHTML');
        const text = filterValue || `${filterType.charAt(0).toUpperCase() + filterType.slice(1)}`;
        button.html(`${icon} ${text}`);
        
        this.renderScheduleTable();
    }

    handleSelectAll(event) {
        const isChecked = event.target.checked;
        $('.store-checkbox').prop('checked', isChecked);
        
        if (isChecked) {
            this.scheduleData.forEach(store => this.selectedStores.add(store.id));
        } else {
            this.selectedStores.clear();
        }
        
        this.updateBulkEditButton();
    }

    handleStoreSelect(event) {
        const storeId = parseInt($(event.target).data('store-id'));
        
        if (event.target.checked) {
            this.selectedStores.add(storeId);
        } else {
            this.selectedStores.delete(storeId);
        }
        
        this.updateBulkEditButton();
    }

    updateBulkEditButton() {
        const count = this.selectedStores.size;
        $('#bulkEditBtn').html(`<i class="fas fa-edit"></i> Bulk Edit (${count})`);
    }

    handleTimeChange(event) {
        // Handle time input changes
        console.log('Time changed:', event.target.value);
        // Add validation and update logic here
    }

    handleAddPeriod(event) {
        const day = $(event.target).data('day');
        console.log('Add period for:', day);
        // Add logic to add a new time period
    }

    handleRemovePeriod(event) {
        $(event.target).closest('.time-slot').remove();
    }

    handleSaveAll() {
        this.showLoading();
        // Simulate API call
        setTimeout(() => {
            this.hideLoading();
            alert('All changes saved successfully!');
        }, 1000);
    }

    handleDiscard() {
        if (confirm('Are you sure you want to discard all changes?')) {
            this.renderScheduleTable();
        }
    }

    handleBulkEdit() {
        if (this.selectedStores.size === 0) {
            alert('Please select stores to edit');
            return;
        }
        console.log('Bulk edit for stores:', Array.from(this.selectedStores));
    }

    handleChangeLog() {
        console.log('Show change log');
    }

    showLoading() {
        $('#loadingOverlay').show();
    }

    hideLoading() {
        $('#loadingOverlay').hide();
    }
}

// Initialize when document is ready
$(document).ready(function() {
    window.storeScheduleManager = new StoreScheduleManager();
});
