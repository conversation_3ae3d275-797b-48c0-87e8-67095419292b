/**
 * Store Detail Page JavaScript
 * Handles data pre-selection and form interactions for store detail page
 */

class StoreDetailManager {
    constructor() {
        this.storeData = window.storeData || {};
        this.storeServices = window.storeServices || {};
        this.metaData = window.metaData || {};
    }

    init() {
        this.initSelect2();
        this.bindEvents();
        this.preSelectData();
    }

    initSelect2() {
        $('.select2').select2({
            theme: 'bootstrap4',
            width: '100%'
        });
    }

    bindEvents() {
        // Tab switching
        $('[data-tab]').on('click', (e) => {
            e.preventDefault();
            this.switchTab($(e.currentTarget));
        });

        // Form submission
        $('#save-all-btn').on('click', () => this.saveForm());

        // Hours checkbox handling
        $('input[type="checkbox"][name*="[closed]"]').on('change', function() {
            const day = $(this).attr('name').match(/hours\[(\w+)\]/)[1];
            const timeInputs = $('input[name*="[' + day + ']"][type="time"]');
            
            if ($(this).is(':checked')) {
                timeInputs.prop('disabled', true).val('');
            } else {
                timeInputs.prop('disabled', false);
            }
        });

        // Market info toggle
        $('input[name="additional_info[markets]"]').on('change', function() {
            if ($(this).val() === '1' && $(this).is(':checked')) {
                $('#market_info_container').slideDown();
            } else {
                $('#market_info_container').slideUp();
            }
        });
    }

    switchTab(tabElement) {
        $('.nav-link').removeClass('active');
        $('.tab-panel').removeClass('active').hide();

        tabElement.addClass('active');
        const targetTab = tabElement.attr('data-tab');
        $('#' + targetTab).addClass('active').show();

        // Pre-select data when switching to specific tabs
        if (targetTab === 'services') {
            setTimeout(() => this.preSelectServices(), 100);
        } else if (targetTab === 'additional-info') {
            setTimeout(() => this.preSelectAdditionalInfo(), 100);
        }
    }

    preSelectData() {
        this.preSelectServices();
        this.preSelectAdditionalInfo();
        this.checkMarketInfo();
    }

    preSelectServices() {
        if (!this.storeServices.services) return;

        // Group services by service group
        const servicesByGroup = {};
        
        this.storeServices.services.forEach(service => {
            const groupId = service.serviceGroup;
            if (!servicesByGroup[groupId]) {
                servicesByGroup[groupId] = [];
            }
            
            if (service.subCategories) {
                service.subCategories.forEach(sub => {
                    servicesByGroup[groupId].push(sub.category || sub.name);
                });
            }
        });

        // Pre-select service groups
        if (servicesByGroup[1]) {
            $('select[name="services[facilities][]"]').val(servicesByGroup[1]).trigger('change');
        }
        if (servicesByGroup[2]) {
            $('select[name="services[accessibility][]"]').val(servicesByGroup[2]).trigger('change');
        }
        if (servicesByGroup[3]) {
            $('select[name="services[atmosphere][]"]').val(servicesByGroup[3]).trigger('change');
        }

        // Pre-select languages and payment methods
        if (this.storeData.languages) {
            const languageIds = this.storeData.languages.map(lang => lang.id);
            $('select[name="services[languages][]"]').val(languageIds).trigger('change');
        }

        if (this.storeData.paymentMethods) {
            const paymentIds = this.storeData.paymentMethods.map(payment => payment.id);
            $('select[name="services[payment_methods][]"]').val(paymentIds).trigger('change');
        }
    }

    preSelectAdditionalInfo() {
        if (!this.storeData) return;

        // Pre-select form fields
        if (this.storeData.origin) {
            $('select[name="additional_info[origin]"]').val(this.storeData.origin).trigger('change');
        }

        if (this.storeData.cuisineTypes) {
            const cuisineValues = Array.isArray(this.storeData.cuisineTypes) 
                ? this.storeData.cuisineTypes 
                : [this.storeData.cuisineTypes];
            $('select[name="additional_info[cuisine][]"]').val(cuisineValues).trigger('change');
        }

        if (this.storeData.budgetRange) {
            $('select[name="additional_info[budget]"]').val(this.storeData.budgetRange).trigger('change');
        }

        if (this.storeData.noWaste !== undefined) {
            $(`input[name="additional_info[no_waste]"][value="${this.storeData.noWaste}"]`).prop('checked', true);
        }

        if (this.storeData.partners) {
            const partnerValues = Array.isArray(this.storeData.partners) 
                ? this.storeData.partners 
                : [this.storeData.partners];
            $('select[name="additional_info[partners][]"]').val(partnerValues).trigger('change');
        }

        if (this.storeData.sellsAtMarkets !== undefined) {
            $(`input[name="additional_info[markets]"][value="${this.storeData.sellsAtMarkets}"]`).prop('checked', true);
            
            if (this.storeData.sellsAtMarkets == 1 && this.storeData.marketInfo) {
                const marketValues = Array.isArray(this.storeData.marketInfo) 
                    ? this.storeData.marketInfo 
                    : [this.storeData.marketInfo];
                $('select[name="additional_info[market_info][]"]').val(marketValues).trigger('change');
            }
        }
    }

    checkMarketInfo() {
        if ($('#markets_yes').is(':checked')) {
            $('#market_info_container').show();
        }
    }

    saveForm() {
        const formData = new FormData($('#store-detail-form')[0]);
        const btn = $('#save-all-btn');

        btn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Saving...');
        btn.prop('disabled', true);

        $.ajax({
            url: window.STORE_API.endpoints.update,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (response) => {
                if (response.success) {
                    this.showAlert('success', 'Store updated successfully!');
                } else {
                    this.showAlert('error', response.message || 'Failed to update store');
                }
            },
            error: (xhr) => {
                this.showAlert('error', 'An error occurred while saving');
            },
            complete: () => {
                btn.html('<i class="fas fa-save mr-2"></i>Save All Changes');
                btn.prop('disabled', false);
            }
        });
    }

    showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const icon = type === 'success' ? 'fa-check' : 'fa-exclamation-triangle';
        
        const alert = $(`
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas ${icon} mr-2"></i>${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `);

        $('.col-md-9').prepend(alert);
        
        setTimeout(() => {
            alert.fadeOut(() => alert.remove());
        }, 5000);
    }
}

// Initialize when document is ready
$(document).ready(() => {
    const storeManager = new StoreDetailManager();
    storeManager.init();
});
