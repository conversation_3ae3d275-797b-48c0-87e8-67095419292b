$(document).ready(function () {
    // Data
    const stores=[
        {id:1,name:'Tech Gadgets Store',email:'<EMAIL>',plan:'Silver',active:true,purchase:'2024-01-15'},
        {id:2,name:'Fashion Boutique',email:'<EMAIL>',plan:'Bronze',active:true,purchase:'2025-05-01'},
        {id:3,name:'Home & Garden',email:'<EMAIL>',plan:'Silver',active:true,purchase:'2025-05-09'}
    ];
    const plans={
        Bronze:['Basic features','Community support','10 products'],
        Silver:['Standard features','Priority support','25 products'],
        Gold:['All Silver + advanced analytics','Dedicated manager','Unlimited products']
    };
    // Utility
    const toISO = d => d.toISOString().split('T')[0];
    function dateAddYear(d){const dt=new Date(d);dt.setFullYear(dt.getFullYear()+1);return dt;}
    function daysUntil(dateStr){if(!dateStr)return null;return Math.floor((new Date(dateStr)-new Date())/86400000);}
    function format(dateStr){if(!dateStr)return '';const d=new Date(dateStr);return d.toLocaleDateString('en-GB');}

    // Tabs
    $('.tab-button').on('click',function(){
        const idx=$(this).index();
        $('.tab-button').removeClass('active').eq(idx).addClass('active');
        $('.tab-content').removeClass('active').eq(idx).addClass('active');
    });

    // Manage tab logic
    let selectedStore=null;

    function renderStoreList(filter=''){
        $('#storeList').empty();
        stores.filter(s=>s.name.toLowerCase().includes(filter.toLowerCase()))
            .forEach(s=>{
                const $item=$('<div>',{class:'store-item'+(selectedStore&&selectedStore.id===s.id?' selected':''),data:{id:s.id}});
                $item.append(`<div class='store-name'>${s.name}</div>`);
                $item.append(`<div style='font-size:.8rem;color:var(--clr-muted)'>${s.email}</div>`);
                const $badges=$('<div>',{style:'margin-top:.3rem;display:flex;gap:.5rem;flex-wrap:wrap'}).appendTo($item);
                if(s.plan) $badges.append(`<span class='badge plan'>${s.plan}</span>`);
                if(s.active) $badges.append(`<span class='badge status success'>Active</span>`);
                $item.on('click',()=>selectStore(s.id));
                $('#storeList').append($item);
            });
    }

    $('#storeSearch').on('input',function(){renderStoreList(this.value);});

    function selectStore(id){
        selectedStore=stores.find(s=>s.id===id);
        renderStoreList($('#storeSearch').val());
        $('#selectedStoreName').text(selectedStore.name);
        $('#planSelect').prop('disabled',false).val(selectedStore.plan||'');
        $('#datePurchased').prop('disabled',false).val(selectedStore.purchase||'');
        computeRenewal();
        $('#assignBtn').prop('disabled',false);
        updateDetailCard();
    }

    function computeRenewal(){
        const purchased = $('#datePurchased').val();
        if (purchased) {
            $('#dateRenewal').val(toISO(dateAddYear(purchased)));
        } else $('#dateRenewal').val('');
    }

    $('#datePurchased').on('change',computeRenewal);

    function updateDetailCard(){
        if(!selectedStore)return;
        $('#detailPlan').text(selectedStore.plan||'—');
        $('#detailPurchase').text(selectedStore.purchase?format(selectedStore.purchase):'—');
        $('#detailRenewal').text(selectedStore.purchase?format(dateAddYear(selectedStore.purchase)):'—');
        $('#planFeatures').empty();
        (plans[selectedStore.plan]||[]).forEach(f=>$('#planFeatures').append(`<li>${f}</li>`));
    }

    $('#assignBtn').on('click',function(){
        if(!selectedStore)return;
        selectedStore.plan=$('#planSelect').val()||null;
        selectedStore.purchase=$('#datePurchased').val()||null;
        selectedStore.active=!!selectedStore.plan;
        alert('Subscription assigned.');
        renderStoreList($('#storeSearch').val());
        updateDetailCard();
        renderOverview();
        renderReminders();
    });

    // Overview renderer
    function renderOverview(){
        $('#overviewBody').empty();
        stores.forEach(s=>{
            const renewal=s.purchase? dateAddYear(s.purchase):null;
            const diff=renewal? daysUntil(renewal):null;
            const diffClass=diff<=0?'negative':'positive';
            const diffText=diff===null?'—':(diff<=0?'Expired':diff+' days');
            $('#overviewBody').append(`<tr>
        <td>${s.name}</td>
        <td>${s.email}</td>
        <td>${s.plan?`<span class='badge plan'>${s.plan}</span>`:'—'}</td>
        <td>${s.active?`<span class='badge success'>Active</span>`:'—'}</td>
        <td>${renewal?format(renewal):'—'}</td>
        <td class='days ${diffClass}'>${diffText}</td>
      </tr>`);
        });
    }

    // Reminders renderer
    function renderReminders(){
        $('#reminderList').empty();
        stores.forEach(s=>{
            const renewal=s.purchase? dateAddYear(s.purchase):null;
            const diff=renewal? daysUntil(renewal):null;
            const headerStatus=diff<=0?'<span class=\'badge danger\'>Expired</span>':`<span class='badge success'>${diff} days remaining</span>`;
            const $card=$('<div>',{class:'reminder-item'});
            $card.append(`<div class='reminder-header'>${s.name}</div>`);
            $card.append(`<div style='color:var(--clr-muted);font-size:.8rem'>Renewal: ${renewal?format(renewal):'—'}</div>`);
            $card.append(`<div style='position:absolute;top:1rem;right:1rem'>${headerStatus}</div>`);
            $card.append('<div style="margin-top:.75rem;font-weight:600;font-size:.85rem">Reminder Schedule:</div>');
            const $sch=$('<div>',{class:'schedule'}).appendTo($card);
            const schedules=[30,7,3];
            schedules.forEach(d=>{
                let pill='Pending',pillClass='';
                if(diff!==null && diff<=0 && d===3) {pill='Urgent';pillClass='urgent';}
                else if(diff!==null && diff<d && diff>0) {pill='Urgent';pillClass='urgent';}
                else if(diff!==null && diff<=0) {pill='Sent';pillClass='sent';}
                $sch.append(`<span><svg width='16' height='16' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><path d='M12 8a4 4 0 1 1-8 0 4 4 0 0 1 8 0z'/><path d='M8 0v2'/><path d='M8 14v2'/><path d='M0 8h2'/><path d='M14 8h2'/></svg> ${d} days before <span class='pill ${pillClass}'>${pill}</span></span>`);
            });
            $('#reminderList').append($card);
        });
    }

    renderStoreList();
    renderOverview();
    renderReminders();
});
