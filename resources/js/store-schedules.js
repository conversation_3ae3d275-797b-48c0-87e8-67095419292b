/**
 * Store Schedules Manager
 * Handles all functionality for the store schedules management page
 */
class StoreSchedulesManager {
    constructor() {
        this.storeData = null;
        this.servicesData = null;
        this.metaData = null;
        this.scheduleData = [];
        this.selectedStores = new Set();
        this.filters = {
            search: '',
            category: '',
            city: '',
            schedule_status: ''
        };
        this.apiEndpoints = window.apiEndpoints || {};
        this.init();
    }

    init() {
        this.loadData();
        this.bindEvents();
        this.initializeFilters();
        this.renderScheduleTable();
    }

    loadData() {
        // Get data from window object (passed from Blade template)
        this.storeData = window.storeDetail || null;
        this.servicesData = window.storeServices || null;
        this.metaData = window.metaData || null;

        console.log('Store Data:', this.storeData);
        console.log('Services Data:', this.servicesData);
        console.log('Meta Data:', this.metaData);

        // Generate mock schedule data for demonstration
        this.generateMockScheduleData();
    }

    generateMockScheduleData() {
        // Mock data based on the UI design shown in the image
        this.scheduleData = [
            {
                id: 1,
                name: 'Downtown Market',
                category: 'Grocery',
                city: 'New York',
                schedule: {
                    monday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '20:00' }],
                    tuesday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '20:00' }],
                    wednesday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '20:00' }],
                    thursday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '20:00' }],
                    friday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '20:00' }],
                    saturday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '20:00' }],
                    sunday: []
                }
            },
            {
                id: 2,
                name: 'Westside Electronics',
                category: 'Electronics',
                city: 'Los Angeles',
                schedule: {
                    monday: [{ open: '09:00', close: '12:30' }, { open: '13:30', close: '19:00' }],
                    tuesday: [{ open: '09:00', close: '12:30' }, { open: '13:30', close: '19:00' }],
                    wednesday: [{ open: '09:00', close: '12:30' }, { open: '13:30', close: '19:00' }],
                    thursday: [{ open: '09:00', close: '12:30' }, { open: '13:30', close: '19:00' }],
                    friday: [{ open: '09:00', close: '12:30' }, { open: '13:30', close: '19:00' }],
                    saturday: [{ open: '09:00', close: '12:30' }, { open: '13:30', close: '19:00' }],
                    sunday: []
                }
            },
            {
                id: 3,
                name: 'Northside Pharmacy',
                category: 'Pharmacy',
                city: 'Chicago',
                schedule: {
                    monday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '18:00' }],
                    tuesday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '18:00' }],
                    wednesday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '18:00' }],
                    thursday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '18:00' }],
                    friday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '18:00' }],
                    saturday: [{ open: '08:00', close: '12:00' }, { open: '13:00', close: '18:00' }],
                    sunday: []
                }
            },
            {
                id: 4,
                name: 'Eastside Cafe',
                category: 'Food & Beverage',
                city: 'Boston',
                schedule: {
                    monday: [{ open: '07:00', close: '20:00' }],
                    tuesday: [{ open: '07:00', close: '20:00' }],
                    wednesday: [{ open: '07:00', close: '20:00' }],
                    thursday: [{ open: '07:00', close: '20:00' }],
                    friday: [{ open: '07:00', close: '20:00' }],
                    saturday: [{ open: '07:00', close: '20:00' }],
                    sunday: [{ open: '10:00', close: '13:00' }, { open: '14:00', close: '19:00' }]
                }
            },
            {
                id: 5,
                name: 'Central Bookstore',
                category: 'Retail',
                city: 'Seattle',
                schedule: {
                    monday: [{ open: '10:00', close: '13:00' }, { open: '14:00', close: '19:00' }],
                    tuesday: [{ open: '10:00', close: '13:00' }, { open: '14:00', close: '19:00' }],
                    wednesday: [{ open: '10:00', close: '13:00' }, { open: '14:00', close: '19:00' }],
                    thursday: [{ open: '10:00', close: '13:00' }, { open: '14:00', close: '19:00' }],
                    friday: [{ open: '10:00', close: '13:00' }, { open: '14:00', close: '19:00' }],
                    saturday: [{ open: '10:00', close: '13:00' }, { open: '14:00', close: '19:00' }],
                    sunday: []
                }
            }
        ];
    }

    bindEvents() {
        // Search functionality
        $('#searchStores').on('input', this.handleSearch.bind(this));
        
        // Filter dropdowns
        $('.dropdown-item').on('click', this.handleFilterChange.bind(this));
        
        // Select all checkbox
        $('#selectAll').on('change', this.handleSelectAll.bind(this));
        
        // Individual store checkboxes
        $(document).on('change', '.store-checkbox', this.handleStoreSelect.bind(this));
        
        // Time input changes
        $(document).on('change', '.time-input', this.handleTimeChange.bind(this));
        
        // Add/remove period buttons
        $(document).on('click', '.add-period-btn', this.handleAddPeriod.bind(this));
        $(document).on('click', '.remove-period-btn', this.handleRemovePeriod.bind(this));
        
        // Action buttons
        $('#saveAllBtn').on('click', this.handleSaveAll.bind(this));
        $('#discardBtn').on('click', this.handleDiscard.bind(this));
        $('#bulkEditBtn').on('click', this.handleBulkEdit.bind(this));
        $('#changeLogBtn').on('click', this.handleChangeLog.bind(this));
        
        // Bulk edit modal
        $('#applyBulkChanges').on('click', this.handleApplyBulkChanges.bind(this));
    }

    initializeFilters() {
        // Populate filter dropdowns with unique values
        const categories = [...new Set(this.scheduleData.map(store => store.category))];
        const cities = [...new Set(this.scheduleData.map(store => store.city))];

        // Populate category dropdown
        const categoryDropdown = $('#categoryDropdown');
        categories.forEach(category => {
            categoryDropdown.append(`<li><a class="dropdown-item" href="#" data-filter="category" data-value="${category}">${category}</a></li>`);
        });

        // Populate city dropdown
        const cityDropdown = $('#cityDropdown');
        cities.forEach(city => {
            cityDropdown.append(`<li><a class="dropdown-item" href="#" data-filter="city" data-value="${city}">${city}</a></li>`);
        });
    }

    renderScheduleTable() {
        const tbody = $('#scheduleTableBody');
        tbody.empty();

        const filteredData = this.getFilteredData();

        filteredData.forEach(store => {
            const row = this.createStoreRow(store);
            tbody.append(row);
        });

        this.updateBulkEditButton();
    }

    createStoreRow(store) {
        const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        
        let row = `
            <tr class="store-row" data-store-id="${store.id}">
                <td>
                    <input type="checkbox" class="form-check-input store-checkbox" data-store-id="${store.id}">
                </td>
                <td>
                    <div class="store-name">${store.name}</div>
                </td>
                <td>
                    <div class="store-category">${store.category}</div>
                </td>
                <td>
                    <div class="store-city">${store.city}</div>
                </td>
        `;

        days.forEach(day => {
            row += `<td class="schedule-day">${this.createDaySchedule(store, day)}</td>`;
        });

        row += '</tr>';
        return row;
    }

    createDaySchedule(store, day) {
        const daySchedule = store.schedule[day];
        
        if (!daySchedule || daySchedule.length === 0) {
            return '<div class="closed-indicator">Closed</div>';
        }

        let scheduleHtml = '';
        daySchedule.forEach((period, index) => {
            scheduleHtml += `
                <div class="time-slot" data-store-id="${store.id}" data-day="${day}" data-period="${index}">
                    <input type="time" class="time-input" value="${period.open}" data-type="open">
                    <span class="time-separator">-</span>
                    <input type="time" class="time-input" value="${period.close}" data-type="close">
                    ${daySchedule.length > 1 ? '<button class="remove-period-btn" title="Remove period"><i class="fas fa-times"></i></button>' : ''}
                </div>
            `;
        });

        scheduleHtml += `<button class="add-period-btn" data-store-id="${store.id}" data-day="${day}">+ Add second period</button>`;
        
        return scheduleHtml;
    }

    getFilteredData() {
        return this.scheduleData.filter(store => {
            const matchesSearch = !this.filters.search || 
                store.name.toLowerCase().includes(this.filters.search.toLowerCase());
            const matchesCategory = !this.filters.category || store.category === this.filters.category;
            const matchesCity = !this.filters.city || store.city === this.filters.city;
            
            let matchesSchedule = true;
            if (this.filters.schedule_status) {
                const hasOpenHours = Object.values(store.schedule).some(day => day.length > 0);
                matchesSchedule = this.filters.schedule_status === 'open' ? hasOpenHours : !hasOpenHours;
            }
            
            return matchesSearch && matchesCategory && matchesCity && matchesSchedule;
        });
    }

    // Event Handlers
    handleSearch(event) {
        this.filters.search = event.target.value;
        this.renderScheduleTable();
    }

    handleFilterChange(event) {
        event.preventDefault();
        const filterType = $(event.target).data('filter');
        const filterValue = $(event.target).data('value');
        
        this.filters[filterType] = filterValue;
        
        // Update button text
        const button = $(event.target).closest('.dropdown').find('.dropdown-toggle');
        const icon = button.find('i').prop('outerHTML');
        const text = filterValue || `${filterType.charAt(0).toUpperCase() + filterType.slice(1)}`;
        button.html(`${icon} ${text}`);
        
        this.renderScheduleTable();
    }

    handleSelectAll(event) {
        const isChecked = event.target.checked;
        $('.store-checkbox').prop('checked', isChecked);
        
        if (isChecked) {
            this.scheduleData.forEach(store => this.selectedStores.add(store.id));
        } else {
            this.selectedStores.clear();
        }
        
        this.updateBulkEditButton();
    }

    handleStoreSelect(event) {
        const storeId = parseInt($(event.target).data('store-id'));
        
        if (event.target.checked) {
            this.selectedStores.add(storeId);
        } else {
            this.selectedStores.delete(storeId);
        }
        
        this.updateBulkEditButton();
    }

    updateBulkEditButton() {
        const count = this.selectedStores.size;
        $('#bulkEditBtn').html(`<i class="fas fa-edit"></i> Bulk Edit (${count})`);
        $('#bulkEditBtn').prop('disabled', count === 0);
    }

    handleTimeChange(event) {
        const timeSlot = $(event.target).closest('.time-slot');
        const storeId = timeSlot.data('store-id');
        const day = timeSlot.data('day');
        const period = timeSlot.data('period');
        const type = $(event.target).data('type');
        const value = event.target.value;

        // Update the data model
        if (this.scheduleData[storeId - 1] && this.scheduleData[storeId - 1].schedule[day][period]) {
            this.scheduleData[storeId - 1].schedule[day][period][type] = value;
        }

        console.log(`Updated ${storeId} ${day} ${period} ${type} to ${value}`);
    }

    handleAddPeriod(event) {
        const storeId = $(event.target).data('store-id');
        const day = $(event.target).data('day');
        
        // Add a new period to the data model
        if (this.scheduleData[storeId - 1] && this.scheduleData[storeId - 1].schedule[day]) {
            this.scheduleData[storeId - 1].schedule[day].push({ open: '09:00', close: '17:00' });
            this.renderScheduleTable();
        }
    }

    handleRemovePeriod(event) {
        const timeSlot = $(event.target).closest('.time-slot');
        const storeId = timeSlot.data('store-id');
        const day = timeSlot.data('day');
        const period = timeSlot.data('period');

        // Remove from data model
        if (this.scheduleData[storeId - 1] && this.scheduleData[storeId - 1].schedule[day]) {
            this.scheduleData[storeId - 1].schedule[day].splice(period, 1);
            this.renderScheduleTable();
        }
    }

    handleSaveAll() {
        this.showLoading();
        
        // Prepare data for API call
        const schedulesToUpdate = this.scheduleData.map(store => ({
            store_id: store.id,
            schedule: store.schedule
        }));

        // Make API call to save schedules
        if (this.apiEndpoints.updateSchedules) {
            $.ajax({
                url: this.apiEndpoints.updateSchedules,
                method: 'PUT',
                data: {
                    schedules: schedulesToUpdate,
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: (response) => {
                    this.hideLoading();
                    if (response.success) {
                        this.showSuccessMessage('All changes saved successfully!');
                    } else {
                        this.showErrorMessage(response.message || 'Failed to save changes');
                    }
                },
                error: (xhr) => {
                    this.hideLoading();
                    this.showErrorMessage('Failed to save changes: ' + xhr.responseText);
                }
            });
        } else {
            // Fallback for demo
            setTimeout(() => {
                this.hideLoading();
                this.showSuccessMessage('All changes saved successfully! (Demo mode)');
            }, 1000);
        }
    }

    handleDiscard() {
        if (confirm('Are you sure you want to discard all changes?')) {
            this.generateMockScheduleData();
            this.renderScheduleTable();
            this.showSuccessMessage('All changes discarded');
        }
    }

    handleBulkEdit() {
        if (this.selectedStores.size === 0) {
            this.showErrorMessage('Please select stores to edit');
            return;
        }
        
        $('#selectedStoreCount').text(this.selectedStores.size);
        $('#bulkEditModal').modal('show');
    }

    handleApplyBulkChanges() {
        // Implementation for applying bulk changes
        console.log('Apply bulk changes to stores:', Array.from(this.selectedStores));
        $('#bulkEditModal').modal('hide');
        this.showSuccessMessage('Bulk changes applied successfully!');
    }

    handleChangeLog() {
        console.log('Show change log');
        this.showInfoMessage('Change log feature coming soon!');
    }

    showLoading() {
        $('#loadingOverlay').show();
    }

    hideLoading() {
        $('#loadingOverlay').hide();
    }

    showSuccessMessage(message) {
        // You can implement a toast notification system here
        alert('Success: ' + message);
    }

    showErrorMessage(message) {
        alert('Error: ' + message);
    }

    showInfoMessage(message) {
        alert('Info: ' + message);
    }
}

// Initialize when document is ready
$(document).ready(function() {
    window.storeSchedulesManager = new StoreSchedulesManager();
});
