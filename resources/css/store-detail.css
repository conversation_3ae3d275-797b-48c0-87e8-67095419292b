/* Store Detail Page Styles */

/* Tab Navigation */
.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
}

.form-group label {
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.form-control-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.card {
    box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
}

.nav-pills .nav-link {
    border-radius: 0.25rem;
    color: #495057;
    padding: 0.5rem 1rem;
    margin-bottom: 2px;
}

.nav-pills .nav-link.active {
    background-color: #007bff;
    color: white;
}

.nav-pills .nav-link:hover {
    background-color: #e9ecef;
    color: #007bff;
}

/* Categories Tree */
.categories-tree {
    background-color: #fafafa;
}

.category-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 10px;
    background-color: white;
    transition: all 0.2s ease;
}

.category-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,.1);
    border-color: #007bff;
}

.category-main .form-check-label {
    font-size: 1rem;
    color: #495057;
    cursor: pointer;
}

.subcategories {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 10px;
    margin-top: 10px;
}

.subcategory-checkbox + label {
    color: #6c757d;
    font-size: 0.9rem;
    cursor: pointer;
    transition: color 0.2s ease;
}

.subcategory-checkbox:checked + label {
    color: #007bff;
    font-weight: 500;
}

.toggle-subcategories {
    color: #6c757d !important;
    text-decoration: none !important;
    padding: 2px 6px !important;
}

.toggle-subcategories:hover {
    color: #007bff !important;
}

#selected-categories-summary .badge {
    font-size: 0.85rem;
    padding: 6px 10px;
    border-radius: 15px;
    background-color: #007bff;
    position: relative;
}

#selected-categories-summary .badge .remove-category {
    color: white !important;
    text-decoration: none !important;
    margin-left: 5px;
    font-size: 0.75rem;
}

#selected-categories-summary .badge .remove-category:hover {
    color: #ffcccc !important;
}

.category-checkbox:checked + label,
.subcategory-checkbox:checked + label {
    color: #007bff;
    font-weight: 500;
}

.btn-group .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

#category-search {
    border-radius: 20px;
    padding: 8px 15px;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
}

#category-search:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.card-tools .btn {
    font-size: 0.75rem;
    padding: 3px 6px;
}

/* Loading animation */
#categories-loading {
    color: #6c757d;
}

.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Select2 improvements */
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(1.5em + 0.5rem + 2px);
}

.select2-container--bootstrap4 .select2-selection--multiple {
    min-height: calc(1.5em + 0.5rem + 2px);
}

.select2-container {
    width: 100% !important;
}

.form-group {
    margin-bottom: 1rem;
}

.row.align-items-center .form-group {
    margin-bottom: 0.5rem;
}

.small {
    font-size: 0.8rem;
}

.store-detail-container {
    background-color: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

.store-profile-sidebar {
    position: sticky;
    top: 20px;
    height: fit-content;
}

.store-profile-sidebar .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.store-profile-sidebar .list-group-item {
    border: none;
    border-bottom: 1px solid #dee2e6;
    padding: 12px 20px;
    transition: all 0.2s ease;
}

.store-profile-sidebar .list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.store-profile-sidebar .list-group-item.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
    transform: translateX(5px);
}

.store-profile-sidebar .list-group-item.active:hover {
    background-color: #0b5ed7;
}

.store-profile-sidebar .list-group-item i {
    width: 20px;
    text-align: center;
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block !important;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.store-detail-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 8px;
    margin-bottom: 20px;
}

.store-detail-card .card-header {
    background-color: #fff;
    border-bottom: 2px solid #f8f9fa;
    padding: 20px;
}

.store-detail-card .card-body {
    padding: 25px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.form-control {
    border: 1px solid #ced4da;
    border-radius: 6px;
    padding: 10px 12px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.badge {
    font-size: 0.875em;
    padding: 6px 12px;
    border-radius: 20px;
}

.hours-row {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
    transition: background-color 0.2s ease;
}

.hours-row:hover {
    background-color: #e9ecef;
}

.hours-row .form-check-input:checked {
    background-color: #dc3545;
    border-color: #dc3545;
}

.photo-gallery .card {
    border: 2px dashed #dee2e6;
    transition: border-color 0.2s ease;
}

.photo-gallery .card:hover {
    border-color: #0d6efd;
}

.subscription-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    padding: 20px;
}

.subscription-info .badge {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.save-button {
    position: sticky;
    top: 20px;
    z-index: 1000;
}

.alert-floating {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    min-width: 300px;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.service-checkbox {
    margin-bottom: 10px;
}

.service-checkbox .form-check-input {
    margin-top: 0.25rem;
}

.service-checkbox .form-check-label {
    font-weight: 500;
    color: #495057;
}

.social-input-group {
    position: relative;
}

.social-input-group i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 3;
}

.social-input-group .form-control {
    padding-left: 40px;
}

.category-badge {
    margin: 2px;
    font-size: 0.9em;
}

.info-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.info-section h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .store-profile-sidebar {
        position: static;
        margin-bottom: 20px;
    }
    
    .store-profile-sidebar .list-group-item {
        text-align: center;
    }
    
    .save-button {
        position: fixed;
        bottom: 20px;
        right: 20px;
        top: auto;
    }
}

/* Loading states */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
} 