@extends('master')

@section('title_prefix') Stores - @endsection

@section('content_header')
    <h1>Stores</h1>
@stop
@section('content')
    <div class="col-12 mt-3 mb-3">
        <div class="col-12 text-right mb-3">
            <a href="{{ route('store.import') }}" class="btn btn-outline-primary btn-add-new">Import Stores</a>
        </div>
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Filter Stores</h3>
            </div>
            <form id="filter-form" method="GET" action="{{ isset($type) ? route('store.type', ['type' => $type]) : route('store.index') }}" class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Store Name</label>
                            <input type="text" name="name" class="form-control" value="{{ request('name') }}" placeholder="Search by name...">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Email</label>
                            <input type="text" name="email" class="form-control" value="{{ request('email') }}" placeholder="Search by email...">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Category</label>
                            <select name="category" class="form-control">
                                <option value="">All Categories</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>{{ $category }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Location</label>
                            <select name="city" class="form-control">
                                <option value="">All Locations</option>
                                @foreach($cities as $city)
                                    <option value="{{ $city }}" {{ request('city') == $city ? 'selected' : '' }}>{{ $city }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Subscription Plan</label>
                            <select name="subscription_plan" class="form-control">
                                <option value="">All Plans</option>
                                @foreach($subscriptionPlans as $plan)
                                    <option value="{{ $plan }}" {{ request('subscription_plan') == $plan ? 'selected' : '' }}>{{ $plan }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Status</label>
                            <select name="status" class="form-control">
                                <option value="">All Status</option>
                                <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>Active</option>
                                <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 text-right">
                        <button type="submit" class="btn btn-primary">Apply Filters</button>
                        <a href="{{ route('store.index') }}" class="btn btn-default">Reset</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="row pb-5">
        @if(session('success'))
            <div class="col-12">
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                    <h5><i class="icon fas fa-check"></i> C'est terminé !</h5>
                    {{ session('success') }}
                </div>
            </div>
        @endif
        <div class="col-12 mt-3">
            <div class="wrapper-c" style="width: 100%; overflow-x: scroll">
                <div id="jsGrid"></div>
            </div>
        </div>
        <div class="col-12 mt-3">
            {{ $data->links() }}
        </div>
    </div>
@endsection
@push('scripts')
    <script type="text/javascript" defer>
        jQuery(document).ready(function ($) {
            const storeData = @json($data);
            const storeItems = storeData.data;

            const gridData = storeItems.map(item => ({
                Name: item.Name,
                Status: item.Status,
                APECode: item.APECode,
                CreatedDate: formatDate(item.CreatedDate, true),

                Membership: item.subscription_plan ? item.subscription_plan.Name : '',
                PhoneNumber: item.address ? item.address.PhoneNumber : '',
                Category: item.main_categories ? item.main_categories.join(', ') : 'N/A',

                Address1: item.address ? item.address.AddressLine1 : '',
                Address2: item.address ? item.address.AddressLine2 : '',
                City: item.address ? item.address.City : '',
                ZipCode: item.address ? item.address.ZipCode : '',
                Longitude: item.address ? item.address.Longitude : '',
                Latitude: item.address ? item.address.Latitude : '',

                ContactEmail: item.employee ? item.employee.Email : '',
                NameOfTheBusiness: item.business ? item.business.Name : '',
                CodeApeNaf: item.business ? item.business.APE : '',
                SirenNumber: item.business ? item.business.Siren : '',
                ContactPhoneNumber: '',
                NameLegalResponsible: item.employee ?
                    item.employee.FirstName + ' ' + item.employee.LastName : '',

                Action: [item.Id, item.Status],

                _raw: item
            }));

            let filteredData = [...gridData];

            const jsGrid = $("#jsGrid").jsGrid({
                width: "5500px",
                autoHeight: true,
                sorting: true,
                paging: false,
                pageSize: 10,
                pageButtonCount: 5,
                data: filteredData,
                fields: [
                    { name: "Name", title: "Name", type: "text" },
                    {
                        name: "Membership",
                        title: "Membership",
                        type: "text",
                        itemTemplate: function(value) {
                            if (!value) return $("<span>").addClass("badge badge-danger").text("No Plan");

                            const membershipColorMap = {
                                "Basic Free Plan": "badge-secondary",
                                "Bronze": "badge-info",
                                "Silver": "badge-primary",
                                "Gold": "badge-warning",
                                "Platinum": "badge-success"
                            };

                            const badgeClass = membershipColorMap[value] || "badge-light";
                            return $("<span>").addClass("badge " + badgeClass).text(value);
                        },
                    },
                    { name: "PhoneNumber", title: "Phone Number", type: "text" },
                    {
                        name: "Category",
                        title: "Category",
                        type: "text",
                        itemTemplate: function(value) {
                            return $("<div>").html(value);
                        },
                        width: "150px"
                    },
                    { name: "Address1", title: "Address 1", type: "text" },
                    { name: "Address2", title: "Address 2", type: "text" },
                    { name: "City", title: "City", type: "text" },
                    { name: "ZipCode", title: "ZipCode", type: "text" },
                    { name: "Longitude", title: "Longitude", type: "text" },
                    { name: "Latitude", title: "Latitude", type: "text" },
                    { name: "ContactEmail", title: "Contact Email", type: "text" },
                    { name: "NameOfTheBusiness", title: "Name Of The Business", type: "text" },
                    { name: "CodeApeNaf", title: "Code Ape Naf", type: "text" },
                    { name: "SirenNumber", title: "Siren Number", type: "text" },
                    { name: "ContactPhoneNumber", title: "Contact Phone Number", type: "text" },
                    { name: "NameLegalResponsible", title: "Name Legal Responsible", type: "text" },
                    { name: "APECode", title: "APE Code", type: "text" },
                    {
                        name: "Status",
                        title: "Status",
                        type: "text",
                        itemTemplate: function(value) {
                            const statusText = value === 1 ? 'Active' : 'Inactive';
                            const buttonClass = value === 1 ?
                                'active btn-outline-success' : 'inactive btn-outline-danger';

                            return $("<button disabled class='status btn btn-block " + buttonClass + "'>")
                                .text(statusText);
                        },
                    },
                    { name: "CreatedDate", title: "Created Date", type: "text" },
                    {
                        name: "Action",
                        itemTemplate: function(value) {
                            if (value[1] !== 2) {
                                const storeId = value[0];
                                const isActive = value[1] === 1;

                                const viewButton = '<button type="button" class="btn btn-info text-white ' +
                                    'btn-open-change-status-modal" data-toggle="modal" ' +
                                    'data-target="#modal-change-status" data-specific-help="' + value + '">' +
                                    'View Detail</button>';

                                const statusButton = isActive ?
                                    '<button type="button" class="btn btn-danger text-white status-btn" ' +
                                    'data-id="' + storeId + '" data-status="0">Deactivate</button>' :
                                    '<button type="button" class="btn btn-success text-white status-btn" ' +
                                    'data-id="' + storeId + '" data-status="1">Activate</button>';

                                return $('<div class="action-box">' + viewButton + statusButton + '</div>');
                            }
                        },
                        width: "150px"
                    }
                ]
            });

            $('.body-field').each(function() {
                const bodyWrapper = $(this).find(".body-field__wrapper");
                const bodyWrapperFullHeight = bodyWrapper.prop("scrollHeight");
                const btnViewMore = $(this).find('.view-more');
                const initialHeight = 200;

                if (bodyWrapperFullHeight <= initialHeight) {
                    btnViewMore.remove();
                } else {
                    btnViewMore.attr('data-height', bodyWrapperFullHeight);
                    btnViewMore.on("click", function() {
                        const btn = $(this);
                        const expandedHeight = $(this).data('height');

                        btn.parent().find('.body-field__wrapper').animate(
                            { height: expandedHeight },
                            500,
                            "linear",
                            function() {
                                btn.remove();
                            }
                        );
                    });
                }
            });

            $(document).on('click', '.status-btn', function() {
                const storeId = $(this).data('id');
                const newStatus = $(this).data('status');

                $.ajax({
                    url: `{{ route('store.updateStatus', ['id' => ':id', 'status' => ':status']) }}`
                        .replace(':id', storeId)
                        .replace(':status', newStatus),
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);

                            window.location.reload();
                        }
                    },
                    error: function() {
                        toastr.error('Failed to update store status');
                    }
                });
            });
        });
    </script>
@endpush
