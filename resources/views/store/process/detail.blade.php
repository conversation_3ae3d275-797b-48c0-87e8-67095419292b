@extends('master')

@section('title_prefix') Import Detail - @endsection

@section('content_header')
    <h1>Import Detail</h1>
@stop
@section('content')
    <div class="row pb-5">
        <div class="col-12">
            <div class="alert
                @if($data['data']['status'] == 'processing')
                    alert-warning
                @elseif($data['data']['status'] == 'failed')
                    alert-danger
                @else
                    alert-success
                @endif
                alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <h5><i class="icon fas fa-check"></i>{{ $data['message'] }}</h5>
            </div>
        </div>
        <div class="col-12 mt-3">
            <div class="wrapper-c" style="width: 100%">
                <div id="jsGrid"></div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    <script type="text/javascript" defer>
        jQuery(document).ready(function ($) {
            const process = @json($data);
            const dataArr = process.data.invalid_row;
            const filteredData = dataArr
                .map(item => ({
                    Line: item.index,
                    Reason: item.invalidFields.join(', ')
                }));

            $("#jsGrid").jsGrid({
                width: "100%",
                autoHeight: true,
                sorting: true,
                paging: false,
                data: filteredData,
                fields: [
                    { name: "Line", title: "Line", type: "text" },
                    { name: "Reason", title: "Reason", type: "text" },
                ]
            });
        });
    </script>
@endpush
