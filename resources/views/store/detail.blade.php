@extends('adminlte::page')

@section('title', 'Store Schedules')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <h1>Store Schedules</h1>
        <div>
            <button type="button" class="btn btn-outline-secondary btn-sm" id="discardBtn">
                <i class="fas fa-times"></i> Discard
            </button>
            <button type="button" class="btn btn-dark btn-sm" id="saveAllBtn">
                <i class="fas fa-save"></i> Save All Changes
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" id="changeLogBtn">
                <i class="fas fa-history"></i> Change Log
            </button>
        </div>
    </div>
    <p class="text-muted">Manage operating hours for all stores in one place</p>
@stop

@section('content')
    <div class="card">
        <div class="card-header">
            <div class="row">
                <div class="col-md-2">
                    <div class="nav nav-tabs" id="schedule-tabs" role="tablist">
                        <button class="nav-link active" id="regular-hours-tab" data-bs-toggle="tab" data-bs-target="#regular-hours" type="button" role="tab">
                            <i class="fas fa-clock"></i> Regular Hours
                        </button>
                        <button class="nav-link" id="holiday-overrides-tab" data-bs-toggle="tab" data-bs-target="#holiday-overrides" type="button" role="tab">
                            <i class="fas fa-calendar-alt"></i> Holiday & Seasonal Overrides
                        </button>
                    </div>
                </div>
                <div class="col-md-10">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Filters & Actions</h5>
                        <div class="d-flex gap-2">
                            <div class="input-group" style="width: 250px;">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchStores" placeholder="Search stores...">
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="categoryFilter" data-bs-toggle="dropdown">
                                    <i class="fas fa-filter"></i> Category
                                </button>
                                <ul class="dropdown-menu" id="categoryDropdown">
                                    <li><a class="dropdown-item" href="#" data-value="">All Categories</a></li>
                                </ul>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="cityFilter" data-bs-toggle="dropdown">
                                    <i class="fas fa-map-marker-alt"></i> City
                                </button>
                                <ul class="dropdown-menu" id="cityDropdown">
                                    <li><a class="dropdown-item" href="#" data-value="">All Cities</a></li>
                                </ul>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="scheduleFilter" data-bs-toggle="dropdown">
                                    <i class="fas fa-calendar"></i> Schedule
                                </button>
                                <ul class="dropdown-menu" id="scheduleDropdown">
                                    <li><a class="dropdown-item" href="#" data-value="">All Schedules</a></li>
                                    <li><a class="dropdown-item" href="#" data-value="open">Open</a></li>
                                    <li><a class="dropdown-item" href="#" data-value="closed">Closed</a></li>
                                </ul>
                            </div>
                            <button class="btn btn-outline-primary" id="bulkEditBtn">
                                <i class="fas fa-edit"></i> Bulk Edit (5)
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body p-0">
            <div class="tab-content" id="schedule-tab-content">
                <!-- Regular Hours Tab -->
                <div class="tab-pane fade show active" id="regular-hours" role="tabpanel">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="scheduleTable">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 40px;">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>Store</th>
                                    <th>Category</th>
                                    <th>City</th>
                                    <th>Monday</th>
                                    <th>Tuesday</th>
                                    <th>Wednesday</th>
                                    <th>Thursday</th>
                                    <th>Friday</th>
                                    <th>Saturday</th>
                                    <th>Sunday</th>
                                </tr>
                            </thead>
                            <tbody id="scheduleTableBody">
                                <!-- Dynamic content will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Holiday & Seasonal Overrides Tab -->
                <div class="tab-pane fade" id="holiday-overrides" role="tabpanel">
                    <div class="p-4 text-center">
                        <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                        <h5>Holiday & Seasonal Overrides</h5>
                        <p class="text-muted">Manage special hours for holidays and seasonal periods</p>
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Holiday Override
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
@stop

@section('js')
<script>
    // Pass data to JavaScript
    window.storeDetail = @json($storeDetail ?? []);
    window.storeServices = @json($storeServices ?? []);
    window.metaData = @json($metaData ?? []);
    window.storeId = @json($storeId ?? null);
</script>
@endsection

@push('styles')
<link href="{{ asset('css/store-detail.css') }}" rel="stylesheet">
@endpush

@push('scripts')
<script src="{{ asset('js/store-detail.js') }}"></script>
@endpush
