@extends('master')

@section('title_prefix') Store Detail - @endsection

@section('content_header')
    <h1>My Account</h1>
@stop

@section('content')
<div class="row">
    <!-- Left Sidebar -->
    <div class="col-md-3">
        <div class="card card-primary card-outline">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user mr-2"></i>My Profile
                </h3>
            </div>
            <div class="card-body p-0">
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a href="#legal-info" class="nav-link active" data-tab="legal-info">
                            <i class="fas fa-file-contract mr-2"></i>
                            Legal Information
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#activity-categories" class="nav-link" data-tab="activity-categories">
                            <i class="fas fa-tags mr-2"></i>
                            Activity Categories
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#photos" class="nav-link" data-tab="photos">
                            <i class="fas fa-images mr-2"></i>
                            Photos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#social-networks" class="nav-link" data-tab="social-networks">
                            <i class="fas fa-share-alt mr-2"></i>
                            Social Networks
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#hours" class="nav-link" data-tab="hours">
                            <i class="fas fa-clock mr-2"></i>
                            Hours
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#description" class="nav-link" data-tab="description">
                            <i class="fas fa-align-left mr-2"></i>
                            Description
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#services" class="nav-link" data-tab="services">
                            <i class="fas fa-concierge-bell mr-2"></i>
                            Services
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#additional-info" class="nav-link" data-tab="additional-info">
                            <i class="fas fa-info-circle mr-2"></i>
                            Additional Information
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- My Subscription Section -->
        <div class="card card-info card-outline">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-credit-card mr-2"></i>My Subscription
                </h3>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="d-inline-block bg-info text-white p-2 rounded mb-2">
                        <i class="fas fa-star fa-lg"></i>
                    </div>
                    <div>
                        <span class="text-muted d-block small">Current Plan</span>
                        @php
                            $planName = $store->subscriptionPlan->Name ?? 'Bronze';
                            $badgeClass = 'badge-light';

                            // Map plan names to badge classes
                            $planColorMap = [
                                "Basic Free Plan" => "badge-secondary",
                                "Bronze" => "badge-info",
                                "Silver" => "badge-primary",
                                "Gold" => "badge-warning",
                                "Platinum" => "badge-success"
                            ];

                            if (isset($planColorMap[$planName])) {
                                $badgeClass = $planColorMap[$planName];
                            }
                        @endphp
                        <span class="badge {{ $badgeClass }} p-2 d-block mb-2">{{ $planName }}</span>
                        <span class="text-muted d-block small">Status</span>
                        <span class="badge {{ $store->Status == 1 ? 'badge-success' : 'badge-danger' }}">
                            {{ $store->Status == 1 ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <form id="store-detail-form">
            @csrf
            <input type="hidden" name="_method" value="POST">

            <!-- Legal Information Tab -->
            <div class="tab-panel active" id="legal-info">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Legal Information</h3>
                        <small class="text-muted d-block">(Contact us for any modifications)</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="business_name">Business Name</label>
                                    <input type="text" class="form-control form-control-sm" id="business_name"
                                           name="business[name]" value="{{ $storeDetail['businessName'] ?? $store->business->Name ?? 'STS' }}" readonly>
                                </div>

                                <div class="form-group">
                                    <label for="address_1">Address 1 (Street number and name...)</label>
                                    <input type="text" class="form-control form-control-sm" id="address_1"
                                           name="address[address_1]" value="{{ $storeDetail['addressLine1'] ?? $store->address->Address1 ?? 'Orchard' }}" readonly>
                                </div>

                                <div class="form-group">
                                    <label for="city">Town</label>
                                    <input type="text" class="form-control form-control-sm" id="town"
                                           name="address[town]" value="{{ $storeDetail['city'] ?? $store->address->City ?? '' }}" readonly>
                                </div>

                                <div class="form-group">
                                    <label for="phone_number">Phone Number</label>
                                    <input type="text" class="form-control form-control-sm" id="phone_number"
                                           name="address[phone_number]" value="{{ $storeDetail['phoneNumber'] ?? $store->address->PhoneNumber ?? '' }}" readonly>
                                </div>

                                <div class="form-group">
                                    <label for="legal_officer">Director/Legal Guardian</label>
                                    <input type="text" class="form-control form-control-sm" id="legal_officer"
                                           name="legal_officer" value="{{ $storeDetail['legalOfficer'] ?? $store->LegalOfficer ?? '' }}" readonly>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="address_2">Address 2 (Building, apartment...)</label>
                                    <input type="text" class="form-control form-control-sm" id="address_2"
                                           name="address[address_2]" value="{{ $storeDetail['addressLine2'] ?? $store->address->Address2 ?? '' }}" readonly>
                                </div>

                                <div class="form-group">
                                    <label for="legal_manager">Manager / Legal Responsible</label>
                                    <input type="text" class="form-control form-control-sm" id="legal_manager"
                                           name="business[legal_manager]" value="{{ $store->business->LegalManager ?? '' }}" readonly>
                                </div>

                                <div class="form-group">
                                    <label for="siret_siren">SIRET or SIREN Number</label>
                                    <input type="text" class="form-control form-control-sm" id="siret_siren"
                                           name="business[siret_siren]" value="{{ $storeDetail['sirenNumber'] ?? $store->business->SiretSiren ?? '' }}" readonly>
                                </div>

                                <div class="form-group">
                                    <label for="ape_code">Code APE</label>
                                    <input type="text" class="form-control form-control-sm" id="ape_code"
                                           name="ape_code" value="{{ $storeDetail['apeCode'] ?? $store->APECode ?? '' }}" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activity Categories Tab -->
            <div class="tab-panel" id="activity-categories" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Activity Categories</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-sm btn-primary" id="expand-all-categories">
                                <i class="fas fa-expand-arrows-alt mr-1"></i>Expand All
                            </button>
                            <button type="button" class="btn btn-sm btn-secondary" id="collapse-all-categories">
                                <i class="fas fa-compress-arrows-alt mr-1"></i>Collapse All
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Search and Filter Section -->
                        <div class="form-group">
                            <label for="category-search">Search Categories</label>
                            <input type="text" class="form-control form-control-sm" id="category-search" placeholder="Type to search categories and services...">
                        </div>

                        <!-- Categories and Services Container -->
                        <div class="form-group">
                            <label>Available Categories & Services</label>
                            <div id="categories-loading" class="text-center py-4">
                                <i class="fas fa-spinner fa-spin mr-2"></i>Loading categories and services...
                            </div>
                            <div id="categories-container" class="categories-tree" style="max-height: 400px; overflow-y: auto; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; display: none;">
                                <!-- Categories and services will be populated here -->
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="form-group">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" id="select-all-visible">
                                    <i class="fas fa-check-double mr-1"></i>Select All Visible
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="deselect-all">
                                    <i class="fas fa-times mr-1"></i>Deselect All
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-info" id="load-popular-categories">
                                    <i class="fas fa-star mr-1"></i>Load Popular
                                </button>
                            </div>
                        </div>

                        <!-- Selected Categories Summary -->
                        <div class="form-group">
                            <label>Currently Selected Categories</label>
                            <div id="selected-categories-summary" class="d-flex flex-wrap">
                                @if($store->categories && $store->categories->count() > 0)
                                    @foreach($store->categories as $category)
                                        <span class="badge badge-primary mr-2 mb-2" data-category-id="{{ $category->Id }}">
                                            {{ $category->MainCategoryName }}
                                            @if($category->SubCategoryName)
                                                : {{ $category->SubCategoryName }}
                                            @endif
                                            <button type="button" class="btn btn-sm btn-link text-white p-0 ml-1 remove-category"
                                                    data-category-id="{{ $category->Id }}">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </span>
                                    @endforeach
                                @else
                                    <span class="text-muted" id="no-categories-selected">No categories selected</span>
                                @endif
                            </div>
                        </div>

                        <!-- Advanced Options -->
                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="auto-select-parent" checked>
                                <label class="form-check-label" for="auto-select-parent">
                                    Automatically select parent category when sub-category is selected
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Photos Tab -->
            <div class="tab-panel" id="photos" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Photos</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="store_photos">Upload Photos</label>
                            <input type="file" class="form-control-file" id="store_photos"
                                   name="photos[]" multiple accept="image/*">
                        </div>

                        <div class="row" id="photo-gallery">
                            @if(isset($storeDetail['images']) && count($storeDetail['images']) > 0)
                                @foreach($storeDetail['images'] as $image)
                                    <div class="col-md-4 mb-3">
                                        <div class="card">
                                            <img src="{{ $image['link'] }}" class="card-img-top" alt="Store Image">
                                            <div class="card-body">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="primary_image" value="{{ $image['id'] }}" {{ $image['isPrimary'] ? 'checked' : '' }}>
                                                    <label class="form-check-label">
                                                        Primary Image
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="col-md-4 mb-3">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <i class="fas fa-image fa-3x text-muted"></i>
                                            <p class="mt-2 text-muted">No photos uploaded</p>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Social Networks Tab -->
            <div class="tab-panel" id="social-networks" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Social Networks</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="website_url">
                                        <i class="fas fa-globe mr-2"></i>Website URL
                                    </label>
                                    <input type="url" class="form-control form-control-sm" id="website_url"
                                           name="social[website]" placeholder="https://..."
                                           value="{{ $storeDetail['siteInternet'] ?? '' }}">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="facebook_url">
                                        <i class="fab fa-facebook mr-2"></i>Facebook URL
                                    </label>
                                    <input type="url" class="form-control form-control-sm" id="facebook_url"
                                           name="social[facebook]" placeholder="https://facebook.com/..."
                                           value="{{ $storeDetail['facebook'] ?? $store->Facebook ?? '' }}">
                                </div>

                                <div class="form-group">
                                    <label for="instagram_url">
                                        <i class="fab fa-instagram mr-2"></i>Instagram URL
                                    </label>
                                    <input type="url" class="form-control form-control-sm" id="instagram_url"
                                           name="social[instagram]" placeholder="https://instagram.com/..."
                                           value="{{ $storeDetail['instagram'] ?? $store->Instagram ?? '' }}">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="twitter_url">
                                        <i class="fab fa-twitter mr-2"></i>Twitter URL
                                    </label>
                                    <input type="url" class="form-control form-control-sm" id="twitter_url"
                                           name="social[twitter]" placeholder="https://twitter.com/..."
                                           value="{{ $storeDetail['xTwitter'] ?? $store->XTwitter ?? '' }}">
                                </div>

                                <div class="form-group">
                                    <label for="linkedin_url">
                                        <i class="fas fa-globe mr-2"></i>LinkedIn URL
                                    </label>
                                    <input type="url" class="form-control form-control-sm" id="linkedin_url"
                                           name="social[linkedin]" placeholder="https://linkedin.com/..."
                                           value="{{ $storeDetail['linkedIn'] ?? '' }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hours Tab -->
            <div class="tab-panel" id="hours" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Business Hours</h3>
                    </div>
                    <div class="card-body">
                        @php
                            $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

                            // Helper function to find hours for a specific day
                            if (!function_exists('findHoursForDay')) {
                                function findHoursForDay($hoursArray, $dayName) {
                                    if (!$hoursArray || !is_array($hoursArray)) {
                                        return null;
                                    }

                                    $dayIndex = array_search(ucfirst($dayName), ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']);

                                    foreach ($hoursArray as $hours) {
                                        if (isset($hours['dayOfWeek']) && $hours['dayOfWeek'] === $dayIndex) {
                                            return $hours;
                                        }
                                    }

                                    return null;
                                }
                            }
                        @endphp

                        @foreach($days as $day)
                            <div class="row align-items-center mb-2">
                                <div class="col-md-2">
                                    <label class="form-control-label font-weight-bold">{{ $day }}</label>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox"
                                               name="hours[{{ strtolower($day) }}][closed]"
                                               id="closed_{{ strtolower($day) }}">
                                        <label class="form-check-label" for="closed_{{ strtolower($day) }}">
                                            Closed
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="row">
                                        <div class="col-6">
                                            <label class="form-control-label small">Morning</label>
                                            <div class="row">
                                                <div class="col-6">
                                                    <input type="time" class="form-control form-control-sm"
                                                           name="hours[{{ strtolower($day) }}][morning_open]"
                                                           placeholder="Open"
                                                           value="@php
                                                               $dayHours = isset($storeDetail['hoursOfOperations']) ? findHoursForDay($storeDetail['hoursOfOperations'], $day) : null;
                                                               $timeValue = '';
                                                               if ($dayHours && isset($dayHours['morningOpenTime'])) {
                                                                   $parts = explode(':', $dayHours['morningOpenTime']);
                                                                   if (count($parts) == 2) {
                                                                       $timeValue = $parts[0] . ':' . str_pad($parts[1], 2, '0', STR_PAD_LEFT);
                                                                   } else {
                                                                       $timeValue = $dayHours['morningOpenTime'];
                                                                   }
                                                               }
                                                               echo $timeValue;
                                                           @endphp">
                                                </div>
                                                <div class="col-6">
                                                    <input type="time" class="form-control form-control-sm"
                                                           name="hours[{{ strtolower($day) }}][morning_close]"
                                                           placeholder="Close"
                                                           value="@php
                                                               $dayHours = isset($storeDetail['hoursOfOperations']) ? findHoursForDay($storeDetail['hoursOfOperations'], $day) : null;
                                                               $timeValue = '';
                                                               if ($dayHours && isset($dayHours['morningCloseTime'])) {
                                                                   $parts = explode(':', $dayHours['morningCloseTime']);
                                                                   if (count($parts) == 2) {
                                                                       $timeValue = $parts[0] . ':' . str_pad($parts[1], 2, '0', STR_PAD_LEFT);
                                                                   } else {
                                                                       $timeValue = $dayHours['morningCloseTime'];
                                                                   }
                                                               }
                                                               echo $timeValue;
                                                           @endphp">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <label class="form-control-label small">Afternoon</label>
                                            <div class="row">
                                                <div class="col-6">
                                                    <input type="time" class="form-control form-control-sm"
                                                           name="hours[{{ strtolower($day) }}][afternoon_open]"
                                                           placeholder="Open"
                                                           value="@php
                                                               $dayHours = isset($storeDetail['hoursOfOperations']) ? findHoursForDay($storeDetail['hoursOfOperations'], $day) : null;
                                                               $timeValue = '';
                                                               if ($dayHours && isset($dayHours['afternoonOpenTime'])) {
                                                                   $parts = explode(':', $dayHours['afternoonOpenTime']);
                                                                   if (count($parts) == 2) {
                                                                       $timeValue = $parts[0] . ':' . str_pad($parts[1], 2, '0', STR_PAD_LEFT);
                                                                   } else {
                                                                       $timeValue = $dayHours['afternoonOpenTime'];
                                                                   }
                                                               }
                                                               echo $timeValue;
                                                           @endphp">
                                                </div>
                                                <div class="col-6">
                                                    <input type="time" class="form-control form-control-sm"
                                                           name="hours[{{ strtolower($day) }}][afternoon_close]"
                                                           placeholder="Close"
                                                           value="@php
                                                               $dayHours = isset($storeDetail['hoursOfOperations']) ? findHoursForDay($storeDetail['hoursOfOperations'], $day) : null;
                                                               $timeValue = '';
                                                               if ($dayHours && isset($dayHours['afternoonCloseTime'])) {
                                                                   $parts = explode(':', $dayHours['afternoonCloseTime']);
                                                                   if (count($parts) == 2) {
                                                                       $timeValue = $parts[0] . ':' . str_pad($parts[1], 2, '0', STR_PAD_LEFT);
                                                                   } else {
                                                                       $timeValue = $dayHours['afternoonCloseTime'];
                                                                   }
                                                               }
                                                               echo $timeValue;
                                                           @endphp">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Description Tab -->
            <div class="tab-panel" id="description" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Store Description</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control" id="description" name="store[description]"
                                      rows="6" placeholder="Detailed description of your store, services, and offerings...">{{ $storeDetail['description'] ?? $store->Description ?? '' }}</textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Services Tab -->
            <div class="tab-panel" id="services" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Services Offered</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><i class="fas fa-building mr-2"></i>Facilities</label>
                                    @php
                                        $facilities = collect($metaData['services'] ?? [])->filter(function($service) {
                                            return $service['serviceGroup'] == 1 && $service['serviceGroupName'] == 'Facilities';
                                        });
                                    @endphp
                                    <select class="form-control select2" name="services[facilities][]" multiple="multiple" data-placeholder="Select facilities">
                                        @foreach($facilities as $facility)
                                            <option value="{{ $facility['category'] }}">
                                                {{ $facility['category'] }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label><i class="fas fa-universal-access mr-2"></i>Accessibility</label>
                                    @php
                                        $accessibility = collect($metaData['services'] ?? [])->filter(function($service) {
                                            return $service['serviceGroup'] == 2 && $service['serviceGroupName'] == 'Accessibility';
                                        });
                                    @endphp
                                    <select class="form-control select2" name="services[accessibility][]" multiple="multiple" data-placeholder="Select accessibility options">
                                        @foreach($accessibility as $access)
                                            <option value="{{ $access['category'] }}">
                                                {{ $access['category'] }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><i class="fas fa-cloud mr-2"></i>Type of Atmosphere</label>
                                    @php
                                        $atmosphere = collect($metaData['services'] ?? [])->filter(function($service) {
                                            return $service['serviceGroup'] == 3 && $service['serviceGroupName'] == 'AmbianceType';
                                        });
                                    @endphp
                                    <select class="form-control select2" name="services[atmosphere][]" multiple="multiple" data-placeholder="Select atmosphere types">
                                        @foreach($atmosphere as $ambiance)
                                            <option value="{{ $ambiance['category'] }}">
                                                {{ $ambiance['category'] }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label><i class="fas fa-language mr-2"></i>Languages Spoken</label>
                                    <select class="form-control select2" name="services[languages][]" multiple="multiple" data-placeholder="Select languages">
                                        @foreach($metaData['languages'] ?? [] as $language)
                                            <option value="{{ $language['id'] }}">{{ $language['name'] }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label><i class="fas fa-credit-card mr-2"></i>Accepted Payment Methods</label>
                                    <select class="form-control select2" name="services[payment_methods][]" multiple="multiple" data-placeholder="Select payment methods">
                                        @foreach($metaData['paymentMethods'] ?? [] as $payment)
                                            <option value="{{ $payment['id'] }}">
                                                {{ $payment['name'] }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information Tab -->
            <div class="tab-panel" id="additional-info" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Additional Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><i class="fas fa-industry mr-2"></i>Origin of manufacture of products</label>
                                    <select class="form-control form-control-sm" name="additional_info[origin]">
                                        <option value="">Select origin</option>
                                        <option value="local">Local</option>
                                        <option value="national">National</option>
                                        <option value="european">European</option>
                                        <option value="international">International</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label><i class="fas fa-utensils mr-2"></i>Type of cuisine</label>
                                    <select class="form-control select2" name="additional_info[cuisine][]" multiple="multiple" data-placeholder="Select cuisine types">
                                        @foreach($metaData['typeOfCuisine'] ?? [] as $cuisine)
                                            <option value="{{ $cuisine['category'] }}">{{ $cuisine['category'] }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label><i class="fas fa-euro-sign mr-2"></i>Average Budget per Person (in €)</label>
                                    <select class="form-control form-control-sm" name="additional_info[budget]">
                                        <option value="">Select budget range</option>
                                        <option value="gratuit">Gratuit</option>
                                        <option value="">0 - 10</option>
                                        <option value="">10 - 20</option>
                                        <option value="">20 - 30</option>
                                        <option value="">30 - 40</option>
                                        <option value="">40 - 50</option>
                                        <option value="">> 50</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><i class="fas fa-recycle mr-2"></i>Do you offer 'no waste' deals?</label>
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="no_waste_yes" name="additional_info[no_waste]" value="1" class="custom-control-input">
                                        <label class="custom-control-label" for="no_waste_yes">Yes</label>
                                    </div>
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="no_waste_no" name="additional_info[no_waste]" value="0" class="custom-control-input">
                                        <label class="custom-control-label" for="no_waste_no">No</label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label><i class="fas fa-handshake mr-2"></i>Choose the Partner(s)</label>
                                    <select class="form-control select2" name="additional_info[partners][]" multiple="multiple" data-placeholder="Select partners">
                                        <option value="partner1">Partner 1</option>
                                        <option value="partner2">Partner 2</option>
                                        <option value="partner3">Partner 3</option>
                                        <option value="partner4">Partner 4</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label><i class="fas fa-store mr-2"></i>Do you sell your products at markets?</label>
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="markets_yes" name="additional_info[markets]" value="1" class="custom-control-input">
                                        <label class="custom-control-label" for="markets_yes">Yes</label>
                                    </div>
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="markets_no" name="additional_info[markets]" value="0" class="custom-control-input">
                                        <label class="custom-control-label" for="markets_no">No</label>
                                    </div>
                                </div>

                                <div class="form-group" id="market_info_container" style="display: none;">
                                    <label><i class="fas fa-map-marker-alt mr-2"></i>Market Information</label>
                                    <select class="form-control select2" name="additional_info[market_info][]" multiple="multiple" data-placeholder="Select markets">
                                        @foreach($metaData['marketInformations'] ?? [] as $market)
                                            <option value="{{ $market['id'] }}">{{ $market['name'] }} ({{ $market['city'] }})</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <!-- Save Button -->
        <div class="row mt-3">
            <div class="col-12">
                <button type="button" id="save-all-btn" class="btn btn-primary">
                    <i class="fas fa-save mr-2"></i>Save All Changes
                </button>
                <a href="{{ route('store.index') }}" class="btn btn-secondary ml-2">
                    <i class="fas fa-arrow-left mr-2"></i>Back to List
                </a>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script>
    window.STORE_API = {
        storeId: "{{ $store->Id }}",
        endpoints: {
            info: "{{ \App\Services\ApiUrlService::getStoreInfo($store->Id) }}",
            detail: "{{ \App\Services\ApiUrlService::getStoreDetail($store->Id) }}",
            masterData: "{{ \App\Services\ApiUrlService::getStoreMasterData() }}",
        }
    };

    // Store data for UI components
    window.storeData = @json($storeDetail ?? []);
    window.storeServices = @json($storeServices ?? []);
    window.metaData = @json($metaData ?? []);
</script>
@endsection

@push('styles')
<link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/css/select2.css">
<link href="{{ asset('vendor/select2/css/select2.min.css') }}" rel="stylesheet">
<link href="{{ asset('vendor/select2-bootstrap4-theme/select2-bootstrap4.min.css') }}" rel="stylesheet">
@vite('resources/css/store-detail.css')
@endpush

@push('scripts')
<script src="//cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/js/select2.min.js"></script>
<script src="{{ asset('vendor/select2/js/select2.full.min.js') }}"></script>
@vite('resources/js/store-detail.js')
<script type="text/javascript">
// Simple initialization - complex logic moved to store-detail.js
$(document).ready(function() {
    // Initialize store detail manager if it exists
    if (typeof StoreDetailManager !== 'undefined') {
        const storeManager = new StoreDetailManager();
        storeManager.init();
    }
});
</script>
@endpush
