@extends('master')

@section('title_prefix') Subscription - @endsection

@section('content_header')
    <h1>Subscription</h1>
@stop

@push('scripts')
    <script src="{{ asset('js/subscription_index.bundle.js') }}"></script>
@endpush

@section('content')
    <p class='description'>Assign and manage subscription plans for stores</p>
    <div class='tabs'>
        <button class='tab-button active'>Manage Subscriptions</button>
        <button class='tab-button'>Overview</button>
        <button class='tab-button'>Reminders</button>
    </div>
    <!-- Manage tab -->
    <div id='tab-manage' class='tab-content active'>
        <div class='grid'>
            <section class='card'>
                <h2>Select Store</h2>
                <p class='description'>Choose a store to manage subscription</p>
                <input id='storeSearch' type='text' placeholder='Search stores…'>
                <div id='storeList' class="mt-2"></div>
            </section>
            <section class='card'>
                <h2>Assign Subscription Plan</h2>
                <p class='description'>Set subscription details for the selected store</p>
                <div class='field'>
                    <label>Managing subscription for:</label>
                    <div id='selectedStoreName'>—</div>
                </div>
                <div class='field'>
                    <label for='planSelect'>Subscription Plan</label>
                    <select id='planSelect' disabled>
                        <option value=''>Select a plan</option>
                        <option value='Bronze'>Bronze</option>
                        <option value='Silver'>Silver</option>
                        <option value='Gold'>Gold</option>
                    </select>
                </div>
                <div class='field'>
                    <label for='datePurchased'>Date Purchased</label>
                    <input type='date' id='datePurchased' disabled>
                </div>
                <div class='field'>
                    <label>Date of Renewal</label>
                    <input type='text' id='dateRenewal' disabled placeholder='Automatically calculated (1 year period)'>
                </div>
                <button class='btn' id='assignBtn'>Assign Subscription</button>
            </section>
        </div>
        <section class='card'>
            <h2>Current Subscription Details</h2>
            <div style='display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1rem;font-size:.9rem;margin-top:1rem'>
                <div><small>Current Plan</small><span id='dsetailPlan'>—</span></div>
                <div><small>Purchase Date</small><span id='detailPurchase'>—</span></div>
                <div><small>Renewal Date</small><span id='detailRenewal'>—</span></div>
            </div>
            <div>
                <small>Plan Features</small>
                <ul id='planFeatures'></ul>
            </div>
        </section>
    </div>
    <!-- Overview tab -->
    <div id='tab-overview' class='tab-content'>
        <section class='card'>
            <h2>Subscription Overview</h2>
            <p class='description'>All stores and their subscription status</p>
            <div>
                <table>
                    <thead>
                    <tr>
                        <th>Store Name</th><th>Email</th><th>Plan</th><th>Status</th><th>Renewal Date</th><th>Days Until Renewal</th>
                    </tr>
                    </thead>
                    <tbody id='overviewBody'></tbody>
                </table>
            </div>
        </section>
    </div>
    <!-- Reminders tab -->
    <div id='tab-reminders' class='tab-content'>
        <section class='card'>
            <h2>Renewal Reminders</h2>
            <p class='description'>Automatic reminder system for subscription renewals</p>
            <div id='reminderList'></div>
        </section>
    </div>
@endsection
