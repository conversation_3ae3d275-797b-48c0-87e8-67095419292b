/* variables ----------------------------------------------------*/
$clr-primary : #0f172a;
$clr-secondary: #6366f1;
$clr-border  : #e5e7eb;
$clr-bg      : #ffffff;
$clr-muted   : #6b7280;
$clr-success : #16a34a;
$clr-danger  : #dc2626;
$radius      : .5rem;

.tabs {
  display: inline-flex;
  gap: .25rem;
  margin-bottom: 1.5rem;

  .tab-button {
    padding: .45rem 1.25rem;
    border: 1px solid $clr-border;
    background: $clr-bg;
    border-radius: $radius;
    font-size: .875rem;
    cursor: pointer;

    &.active {
      background: $clr-secondary;
      color: #fff;
      border-color: $clr-secondary;
    }
  }
}

.field {
  margin: 5px 0;
}

.tab-content {
  display: none;
  animation: fade .15s ease-in;

  &.active { display: block; }
}

.card {
  border: 1px solid $clr-border;
  border-radius: $radius;
  padding: 1.25rem;
  background: $clr-bg;
}

.grid {
  display: grid;
  gap: 1.5rem;

  @media (min-width: 1200px) { grid-template-columns: 1fr 1fr; }
}

input,
select {
  border: 1px solid $clr-border;
  border-radius: $radius;
  padding: .55rem .75rem;
  font-size: .9rem;
  width: 100%;

  &:disabled {
    background: #f9fafb;
    color: $clr-muted;
  }
}

#assignBtn {
  display: inline-block;
  width: 100%;
  padding: .6rem 1rem;
  background: #8A8A8C;
  color: #fff;
  border: none;
  border-radius: $radius;
  cursor: pointer;
  font-size: .9rem;

  &:disabled { opacity: .4; cursor: not-allowed; }
}

.badge {
  display: inline-block;
  font-size: .7rem;
  padding: .15rem .55rem;
  border-radius: $radius;
  background: #f3f4f6;

  &.plan    { background: #092432; }
  &.success { background: #dcfce7; color: $clr-success; font-weight: 600; }
  &.danger  { background: #fee2e2; color: $clr-danger;  font-weight: 600; }
}

.store-item {
  border: 1px solid $clr-border;
  border-radius: $radius;
  padding: .75rem;
  margin-bottom: .5rem;
  cursor: pointer;
  transition: background .15s, border .15s;
  position: relative;
  .badge.status {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
  }

  &:hover    { background: #f9fafb; }
  &.selected { background: #eef2ff; border-color: $clr-secondary; }
}

.store-name { font-weight: 600; }

table {
  width: 100%;
  border-collapse: collapse;
  font-size: .9rem;

  thead { background: #f9fafb; }

  th,
  td {
    padding: .75rem .5rem;
    text-align: left;
    border-bottom: 1px solid $clr-border;
  }

  .days {
    &.negative { color: $clr-danger;  font-weight: 600; }
    &.positive { color: $clr-success; }
  }
}

.reminder-item {
  border: 1px solid $clr-border;
  border-radius: $radius;
  padding: 1rem;
  margin-bottom: 1rem;
  position: relative;
}

.reminder-header { font-weight: 600; margin-bottom: .25rem; }

.schedule {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  margin-top: .5rem;
  font-size: .85rem;

  span { display: inline-flex; align-items: center; gap: .25rem; }
}

.pill {
  font-size: .65rem;
  padding: .1rem .4rem;
  border-radius: 9999px;
  background: #e5e7eb;
  color: #111;

  &.sent   { background: #000;     color: #fff; }
  &.urgent { background: #fee2e2; color: $clr-danger; }
}
