/* Store Schedule Management Styles */

.store-detail-container {
    background-color: #f8f9fa;
    min-height: 100vh;
}

/* Header Styles */
.content-header {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 1.5rem 0;
    margin-bottom: 0;
}

.content-header h1 {
    font-size: 1.75rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.content-header .text-muted {
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

/* Tab Navigation */
.nav-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 1rem 1.5rem;
    background: none;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #495057;
    background-color: #f8f9fa;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background-color: white;
}

/* Filter Section */
.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1.5rem;
}

.input-group-text {
    background-color: white;
    border-right: none;
    color: #6c757d;
}

.form-control {
    border-left: none;
    box-shadow: none;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.dropdown-toggle {
    border: 1px solid #ced4da;
    background-color: white;
    color: #495057;
}

.dropdown-toggle:hover,
.dropdown-toggle:focus {
    background-color: #f8f9fa;
    border-color: #007bff;
}

/* Schedule Table */
.table-responsive {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
    font-weight: 600;
    color: #495057;
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.table tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Store Row Styles */
.store-row {
    transition: all 0.2s ease;
}

.store-row.selected {
    background-color: #e3f2fd !important;
}

.store-checkbox {
    transform: scale(1.1);
}

.store-name {
    font-weight: 600;
    color: #2c3e50;
}

.store-category {
    color: #6c757d;
    font-size: 0.85rem;
}

.store-city {
    color: #495057;
}

/* Schedule Time Inputs */
.schedule-day {
    min-width: 140px;
    padding: 0.5rem;
}

.time-slot {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.time-slot:last-child {
    margin-bottom: 0;
}

.time-input {
    width: 70px;
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    text-align: center;
}

.time-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.25);
}

.time-separator {
    color: #6c757d;
    font-weight: 500;
}

.closed-indicator {
    color: #dc3545;
    font-weight: 500;
    font-style: italic;
}

.add-period-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border: 1px dashed #007bff;
    background: none;
    color: #007bff;
    border-radius: 0.25rem;
    cursor: pointer;
    width: 100%;
}

.add-period-btn:hover {
    background-color: #e3f2fd;
}

.remove-period-btn {
    color: #dc3545;
    background: none;
    border: none;
    padding: 0.25rem;
    cursor: pointer;
    font-size: 0.8rem;
}

.remove-period-btn:hover {
    background-color: #f8d7da;
    border-radius: 0.25rem;
}

/* Invalid Schedule Indicator */
.invalid-schedule {
    color: #dc3545;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Button Styles */
.btn-sm {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

.btn-dark {
    background-color: #2c3e50;
    border-color: #2c3e50;
}

.btn-dark:hover {
    background-color: #1a252f;
    border-color: #1a252f;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .schedule-day {
        min-width: 120px;
    }
    
    .time-input {
        width: 60px;
        font-size: 0.75rem;
    }
}

@media (max-width: 992px) {
    .card-header .row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-tabs {
        flex-direction: column;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    .content-header .d-flex {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start !important;
    }
    
    .schedule-day {
        min-width: 100px;
    }
    
    .time-input {
        width: 50px;
        font-size: 0.7rem;
    }
}
