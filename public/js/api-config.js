/**
 * API Configuration - Centralized endpoint definitions
 *
 * This module provides a centralized way to manage API endpoints
 * for the Viking Store System integration.
 *
 * <AUTHOR> MVP Admin
 * @version 1.0.0
 */
const API_CONFIG = {
    baseUrl: '/api/Integration',

    endpoints: {
        store: {
            info: (id) => `/api/Integration/store/${id}/info`,
            detail: (id) => `/api/Integration/${id}/store`,
            masterData: () => `/api/Integration/store/master-data`,
            categories: (id) => `/api/Integration/store/${id}/categories`
        }
    },

    /**
     * Helper method to build full URLs
     * @param {string} category - The category of endpoints (e.g., 'store')
     * @param {string} endpoint - The specific endpoint name
     * @param {number|null} id - Optional ID parameter
     * @returns {string} The complete URL
     */
    getUrl: function(category, endpoint, id = null) {
        if (typeof this.endpoints[category][endpoint] === 'function') {
            return this.endpoints[category][endpoint](id);
        }
        return this.endpoints[category][endpoint];
    },

    getStoreInfo: (id) => `/api/Integration/store/${id}/info`,
    getStoreDetail: (id) => `/api/Integration/${id}/store`,
    getStoreMasterData: () => `/api/Integration/store/master-data`,
    getStoreCategories: (id) => `/api/Integration/store/${id}/categories`
};

window.API_CONFIG = API_CONFIG;
