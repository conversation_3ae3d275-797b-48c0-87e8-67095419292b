/**
 * Store API Service - Centralized API calls
 *
 * This class provides a centralized way to make API calls to the Viking Store System.
 * It handles URL building with dynamic parameters and provides methods for common
 * store-related API operations.
 *
 * <AUTHOR> MVP Admin
 * @version 1.0.0
 */
class StoreApiService {
    constructor() {
        this.baseUrl = '/api/Integration';
    }

    /**
     * Build dynamic URLs with parameters
     * @param {string} template - URL template with placeholders like {id}
     * @param {Object} params - Parameters to replace in the template
     * @returns {string} The complete URL with parameters replaced
     */
    buildUrl(template, params = {}) {
        let url = template;
        Object.keys(params).forEach(key => {
            url = url.replace(`{${key}}`, params[key]);
        });
        return url;
    }

    // API Endpoints Templates
    endpoints = {
        storeInfo: '/api/Integration/store/{id}/info',
        storeDetail: '/api/Integration/{id}/store',
        storeMasterData: '/api/Integration/store/master-data',
        storeCategories: '/api/Integration/store/{id}/categories'
    };

    // Store Info Methods
    async getStoreInfo(storeId) {
        try {
            const url = this.buildUrl(this.endpoints.storeInfo, { id: storeId });
            const response = await fetch(url);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            return await response.json();
        } catch (error) {
            console.error('Error fetching store info:', error);
            throw error;
        }
    }

    async getStoreDetail(storeId) {
        try {
            const url = this.buildUrl(this.endpoints.storeDetail, { id: storeId });
            const response = await fetch(url);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            return await response.json();
        } catch (error) {
            console.error('Error fetching store detail:', error);
            throw error;
        }
    }

    async getStoreMasterData() {
        try {
            const response = await fetch(this.endpoints.storeMasterData);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            return await response.json();
        } catch (error) {
            console.error('Error fetching store master data:', error);
            throw error;
        }
    }

    async getStoreCategories(storeId) {
        try {
            const url = this.buildUrl(this.endpoints.storeCategories, { id: storeId });
            const response = await fetch(url);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            return await response.json();
        } catch (error) {
            console.error('Error fetching store categories:', error);
            throw error;
        }
    }

    // jQuery-based methods for backward compatibility
    getStoreInfoJQuery(storeId) {
        const url = this.buildUrl(this.endpoints.storeInfo, { id: storeId });
        return $.get(url);
    }

    getStoreDetailJQuery(storeId) {
        const url = this.buildUrl(this.endpoints.storeDetail, { id: storeId });
        return $.get(url);
    }

    getStoreMasterDataJQuery() {
        return $.get(this.endpoints.storeMasterData);
    }

    getStoreCategoriesJQuery(storeId) {
        const url = this.buildUrl(this.endpoints.storeCategories, { id: storeId });
        return $.get(url);
    }

    // Batch load all store data
    async loadAllStoreData(storeId) {
        try {
            const [info, detail, masterData, categories] = await Promise.all([
                this.getStoreInfo(storeId),
                this.getStoreDetail(storeId),
                this.getStoreMasterData(),
                this.getStoreCategories(storeId)
            ]);
            
            return {
                info,
                detail,
                masterData,
                categories
            };
        } catch (error) {
            console.error('Error loading all store data:', error);
            throw error;
        }
    }
}

// Create global instance
window.storeApiService = new StoreApiService();

// Export for module usage if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StoreApiService;
} 