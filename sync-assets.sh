#!/bin/bash

# Asset Sync Script
# This script copies assets from resources/ to public/ to keep them in sync
# Run this after making changes to store-detail assets

echo "🔄 Syncing store detail assets..."

# Copy CSS
if [ -f "resources/css/store-detail.css" ]; then
    cp resources/css/store-detail.css public/css/store-detail.css
    echo "✅ CSS synced: resources/css/store-detail.css → public/css/store-detail.css"
else
    echo "❌ Source CSS not found: resources/css/store-detail.css"
fi

# Copy JavaScript
if [ -f "resources/js/store-detail.js" ]; then
    cp resources/js/store-detail.js public/js/store-detail.js
    echo "✅ JS synced: resources/js/store-detail.js → public/js/store-detail.js"
else
    echo "❌ Source JS not found: resources/js/store-detail.js"
fi

echo "🎉 Asset sync complete!"
echo ""
echo "📝 Remember:"
echo "   - Edit files in resources/ directory"
echo "   - Run this script after making changes"
echo "   - Files in public/ are auto-generated"
